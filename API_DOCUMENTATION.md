# 柠汐认证平台 OAuth API 文档

## 概述

柠汐认证平台提供完整的OAuth-like扫码登录API，允许第三方应用集成我们的用户认证系统。用户通过扫描二维码在我们的平台进行授权，第三方应用可获取用户的完整信息。

**基础URL**: `https://auth.ly-y.cn`

## 认证流程

```mermaid
sequenceDiagram
    participant App as 第三方应用
    participant API as 柠汐认证API
    participant User as 用户
    participant Auth as 柠汐认证平台

    App->>API: 1. 注册应用获取凭据
    API-->>App: 返回 client_id & client_secret
    
    App->>API: 2. 请求授权URL
    API-->>App: 返回 auth_url & request_id
    
    App->>App: 3. 生成二维码(auth_url)
    User->>Auth: 4. 扫码访问授权页面
    Auth->>User: 5. 显示授权确认页面
    User->>Auth: 6. 确认授权
    
    loop 轮询状态
        App->>API: 7. 查询授权状态
        API-->>App: 返回状态信息
    end
    
    API-->>App: 8. 返回用户完整信息
```

## API 接口

### 1. 注册第三方应用

**接口**: `POST /api/oauth/register`

**描述**: 注册第三方应用，获取客户端凭据

**请求参数**:
```json
{
    "app_name": "我的应用",
    "app_description": "应用描述（可选）",
    "callback_urls": ["https://myapp.com/callback"],
    "contact_email": "<EMAIL>"
}
```

**响应示例**:
```json
{
    "success": true,
    "client_id": "app_1234567890abcdef",
    "client_secret": "very_long_secret_string_here",
    "message": "应用注册成功，请妥善保管client_secret"
}
```

### 2. 请求用户授权

**接口**: `POST /api/oauth/authorize`

**描述**: 获取用户授权URL，第三方应用将此URL生成二维码

**请求参数**:
```json
{
    "client_id": "app_1234567890abcdef",
    "client_secret": "your_client_secret",
    "callback_url": "https://myapp.com/oauth/callback",
    "app_name": "我的应用"
}
```

**响应示例**:
```json
{
    "success": true,
    "request_id": "req_uuid_here",
    "auth_url": "https://auth.ly-y.cn/qr-auth.html?request_id=req_uuid_here&client_id=app_1234567890abcdef&app_name=我的应用",
    "expires_in": 600,
    "polling_url": "https://auth.ly-y.cn/api/oauth/status/req_uuid_here",
    "message": "请将auth_url生成二维码供用户扫描"
}
```

### 3. 查询授权状态

**接口**: `GET /api/oauth/status/{request_id}`

**描述**: 轮询查询用户授权状态，获取用户信息

**响应示例**:

**等待授权时**:
```json
{
    "success": true,
    "status": "pending",
    "request_id": "req_uuid_here",
    "client_id": "app_1234567890abcdef",
    "app_name": "我的应用"
}
```

**授权成功时**:
```json
{
    "success": true,
    "status": "authorized",
    "request_id": "req_uuid_here",
    "client_id": "app_1234567890abcdef",
    "app_name": "我的应用",
    "user": {
        "user_id": "user_123",
        "username": "张三",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "authorized_at": "2025-01-08T10:30:00"
    }
}
```

**授权被拒绝时**:
```json
{
    "success": true,
    "status": "denied",
    "request_id": "req_uuid_here",
    "client_id": "app_1234567890abcdef",
    "app_name": "我的应用"
}
```

**请求过期时**:
```json
{
    "success": true,
    "status": "expired",
    "request_id": "req_uuid_here",
    "client_id": "app_1234567890abcdef",
    "app_name": "我的应用"
}
```

### 4. API文档接口

**接口**: `GET /api/oauth/docs`

**描述**: 获取完整的API文档信息

## 状态码说明

| 状态 | 说明 |
|------|------|
| pending | 等待用户扫码授权 |
| authorized | 用户已授权，可获取用户信息 |
| denied | 用户拒绝授权 |
| expired | 授权请求已过期 |

## 错误码说明

| HTTP状态码 | 错误信息 | 说明 |
|------------|----------|------|
| 400 | 缺少必需参数 | 请求参数不完整 |
| 401 | 无效的客户端凭据 | client_id或client_secret错误 |
| 404 | 无效的请求ID | request_id不存在或已过期 |
| 500 | 服务器错误 | 内部服务器错误 |

## 集成示例

### Python示例

```python
import requests
import qrcode
import time
from PIL import Image

class NingxiAuth:
    def __init__(self, client_id, client_secret):
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = "https://auth.ly-y.cn"
    
    def request_authorization(self, callback_url, app_name="我的应用"):
        """请求用户授权"""
        url = f"{self.base_url}/api/oauth/authorize"
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "callback_url": callback_url,
            "app_name": app_name
        }
        
        response = requests.post(url, json=data)
        return response.json()
    
    def generate_qr_code(self, auth_url, filename="qr_code.png"):
        """生成二维码"""
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(auth_url)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img.save(filename)
        return filename
    
    def poll_status(self, request_id, timeout=300):
        """轮询授权状态"""
        url = f"{self.base_url}/api/oauth/status/{request_id}"
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            response = requests.get(url)
            result = response.json()
            
            if result.get('status') == 'authorized':
                return result.get('user')
            elif result.get('status') in ['denied', 'expired']:
                return None
            
            time.sleep(2)  # 每2秒查询一次
        
        return None

# 使用示例
auth = NingxiAuth("your_client_id", "your_client_secret")

# 1. 请求授权
auth_result = auth.request_authorization("https://myapp.com/callback")
if auth_result.get('success'):
    # 2. 生成二维码
    qr_file = auth.generate_qr_code(auth_result['auth_url'])
    print(f"二维码已生成: {qr_file}")
    
    # 3. 轮询状态
    user_info = auth.poll_status(auth_result['request_id'])
    if user_info:
        print(f"用户授权成功: {user_info}")
    else:
        print("授权失败或超时")
```

### JavaScript示例

```javascript
class NingxiAuth {
    constructor(clientId, clientSecret) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.baseUrl = "https://auth.ly-y.cn";
    }

    async requestAuthorization(callbackUrl, appName = "我的应用") {
        const response = await fetch(`${this.baseUrl}/api/oauth/authorize`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                client_id: this.clientId,
                client_secret: this.clientSecret,
                callback_url: callbackUrl,
                app_name: appName
            })
        });

        return await response.json();
    }

    generateQRCode(authUrl, containerId) {
        // 使用qrcode.js生成二维码
        QRCode.toCanvas(document.getElementById(containerId), authUrl, {
            width: 200,
            height: 200
        });
    }

    async pollStatus(requestId, timeout = 300000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const response = await fetch(`${this.baseUrl}/api/oauth/status/${requestId}`);
            const result = await response.json();

            if (result.status === 'authorized') {
                return result.user;
            } else if (['denied', 'expired'].includes(result.status)) {
                return null;
            }

            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        return null;
    }
}

// 使用示例
const auth = new NingxiAuth('your_client_id', 'your_client_secret');

async function startAuth() {
    try {
        // 1. 请求授权
        const authResult = await auth.requestAuthorization('https://myapp.com/callback');

        if (authResult.success) {
            // 2. 生成二维码
            auth.generateQRCode(authResult.auth_url, 'qr-container');

            // 3. 轮询状态
            const userInfo = await auth.pollStatus(authResult.request_id);

            if (userInfo) {
                console.log('用户授权成功:', userInfo);
                // 处理用户信息
            } else {
                console.log('授权失败或超时');
            }
        }
    } catch (error) {
        console.error('授权过程出错:', error);
    }
}
```

### PHP示例

```php
<?php
class NingxiAuth {
    private $clientId;
    private $clientSecret;
    private $baseUrl = "https://auth.ly-y.cn";

    public function __construct($clientId, $clientSecret) {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
    }

    public function requestAuthorization($callbackUrl, $appName = "我的应用") {
        $url = $this->baseUrl . "/api/oauth/authorize";
        $data = [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'callback_url' => $callbackUrl,
            'app_name' => $appName
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    public function pollStatus($requestId, $timeout = 300) {
        $startTime = time();
        $url = $this->baseUrl . "/api/oauth/status/" . $requestId;

        while (time() - $startTime < $timeout) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $response = curl_exec($ch);
            curl_close($ch);

            $result = json_decode($response, true);

            if ($result['status'] === 'authorized') {
                return $result['user'];
            } elseif (in_array($result['status'], ['denied', 'expired'])) {
                return null;
            }

            sleep(2);
        }

        return null;
    }
}

// 使用示例
$auth = new NingxiAuth('your_client_id', 'your_client_secret');

$authResult = $auth->requestAuthorization('https://myapp.com/callback');
if ($authResult['success']) {
    echo "请扫描二维码: " . $authResult['auth_url'] . "\n";

    $userInfo = $auth->pollStatus($authResult['request_id']);
    if ($userInfo) {
        echo "用户授权成功: " . json_encode($userInfo) . "\n";
    } else {
        echo "授权失败或超时\n";
    }
}
?>
```
