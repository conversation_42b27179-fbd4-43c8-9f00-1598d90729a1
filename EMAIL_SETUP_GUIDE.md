# 📧 邮件服务配置指南

## 🎯 概述

柠汐认证平台已集成阿里云邮件推送服务，支持发送真实的邮箱验证码。本指南将帮助您完成邮件服务的配置。

## 🚀 快速开始

### 1. 配置邮件服务

运行配置助手：
```bash
python email_config_helper.py
```

选择"1. 配置阿里云邮件推送服务"，按提示填写配置信息。

### 2. 必需的配置信息

您需要准备以下信息：

#### 2.1 阿里云AccessKey
- **AccessKeyId**：访问密钥ID
- **AccessKeySecret**：访问密钥Secret

获取方式：
1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 点击右上角头像 → AccessKey管理
3. 创建AccessKey

#### 2.2 邮件推送服务配置
- **发信域名**：您的域名（需要验证）
- **发信地址**：如 `<EMAIL>`

配置方式：
1. 开通 [阿里云邮件推送服务](https://dm.console.aliyun.com/)
2. 添加并验证您的域名
3. 创建发信地址

### 3. 地域选择

支持以下地域：
- **华东1（杭州）**：`cn-hangzhou` - 推荐国内用户
- **新加坡**：`ap-southeast-1` - 推荐亚太用户
- **美国（新）**：`us-east-1` - 推荐美洲用户
- **德国（法兰克福）**：`eu-central-1` - 推荐欧洲用户

## 📋 配置步骤

### 步骤1：运行配置助手
```bash
python email_config_helper.py
```

### 步骤2：选择地域
根据您的用户群体选择合适的地域。

### 步骤3：填写AccessKey信息
输入您的AccessKeyId和AccessKeySecret。

### 步骤4：配置发信地址
输入您已验证的发信地址。

### 步骤5：设置发件人别名
设置邮件显示的发件人名称，默认为"柠汐认证平台"。

### 步骤6：启用服务
确认启用邮件服务。

## 🧪 测试验证

### 配置验证
```bash
python email_config_helper.py
```
选择"2. 查看当前配置"检查配置状态。

### 功能测试
```bash
python email_config_helper.py
```
选择"3. 测试邮件服务"进行发送测试。

### 集成测试
1. 启动应用：`python app/app.py`
2. 访问注册页面
3. 选择邮箱注册
4. 输入邮箱地址并发送验证码
5. 检查邮箱是否收到验证码

## 📧 邮件模板

### 验证码邮件特点
- **精美的HTML模板**：响应式设计，支持各种邮件客户端
- **品牌一致性**：使用柠汐认证平台的视觉风格
- **安全提醒**：包含安全使用提示
- **有效期提醒**：明确标注验证码有效期（5分钟）

### 邮件内容
- **主题**：【柠汐认证】邮箱验证码
- **发件人**：柠汐认证平台 <<EMAIL>>
- **内容**：包含6位数字验证码和安全提醒

## 🔧 故障排除

### 常见问题

#### 1. 配置不完整
**现象**：提示"配置不完整"
**解决**：检查AccessKeyId、AccessKeySecret、发信地址是否都已填写

#### 2. AccessKey错误
**现象**：提示"AccessKeyId不存在"或"签名不匹配"
**解决**：
- 检查AccessKeyId是否正确
- 检查AccessKeySecret是否正确
- 确认AccessKey状态为"启用"

#### 3. 发信地址错误
**现象**：提示"发信地址格式错误"
**解决**：
- 确认发信地址格式正确（如：<EMAIL>）
- 确认域名已在阿里云邮件推送控制台验证
- 确认发信地址已创建并启用

#### 4. 超出发送额度
**现象**：提示"超出当日邮件发送额度"
**解决**：
- 检查阿里云控制台的发送统计
- 升级邮件推送服务套餐
- 等待次日额度重置

### 调试方法

1. **查看配置**：
   ```bash
   python email_config_helper.py
   ```
   选择"2. 查看当前配置"

2. **测试连接**：
   ```bash
   python email_config_helper.py
   ```
   选择"3. 测试邮件服务"

3. **查看日志**：
   启动应用时观察控制台输出的邮件发送日志

## 💰 费用说明

### 免费额度
- **每月200封**：免费发送额度
- **适用场景**：小型应用、测试环境

### 付费套餐
- **按量付费**：超出免费额度后按发送量计费
- **包年套餐**：大量发送需求可选择包年套餐
- **详细价格**：请参考 [阿里云邮件推送定价](https://www.aliyun.com/price/product#/dm/detail)

## 🔒 安全建议

### AccessKey安全
1. **定期轮换**：建议每3-6个月更换AccessKey
2. **权限最小化**：使用RAM子账号，仅授予邮件推送权限
3. **安全存储**：不要在代码中硬编码AccessKey

### 发信安全
1. **内容合规**：避免敏感词汇，遵守反垃圾邮件规范
2. **频率控制**：设置合理的发信频率限制
3. **监控告警**：配置异常发送告警

### 数据安全
1. **传输加密**：使用HTTPS协议
2. **存储安全**：验证码等敏感信息及时清理
3. **访问控制**：限制邮件服务的访问权限

---

**注意**：请确保您的域名已完成备案（如选择国内地域），并遵守相关法律法规。
