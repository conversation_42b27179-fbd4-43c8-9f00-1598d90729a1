# 🍋 柠汐认证平台 - 短信服务配置指南

## 📋 概述

柠汐认证平台已集成短信宝API，支持发送真实的短信验证码。按照以下步骤完成配置即可启用短信功能。

## 🚀 快速开始

### 1. 注册短信宝账号
- 访问 https://www.smsbao.com/reg 注册账号
- 登录后台进行充值

### 2. 配置短信服务
运行配置助手：
```bash
python sms_config_helper.py
```

按照提示输入：
- 短信宝用户名
- 密码或ApiKey
- 短信签名（已预设为【重庆永柠科技】）
- 启用服务

### 3. 测试短信功能
```bash
python test_sms.py
```

## 📝 短信格式

系统已按照您的要求配置短信格式：

**签名**: 【重庆永柠科技】
**正文**: 亲爱的{user_name}，感谢您选择柠汐认证，您的验证码是{code}。有效期为{time}，请尽快验证！

**完整示例**:
```
【重庆永柠科技】亲爱的张三，感谢您选择柠汐认证，您的验证码是123456。有效期为5分钟，请尽快验证！
```

## ⚙️ 配置文件说明

配置文件位置：`data/sms_config.json`

### 必填配置项：
- `username`: 短信宝用户名
- `password`: 密码MD5值 或 `api_key`: ApiKey（二选一）
- `enabled`: 设置为 `true` 启用服务

### 可选配置项：
- `goods_id`: 专用通道产品ID
- `signature`: 短信签名（默认：【重庆永柠科技】）

## 🔧 手动配置示例

如果您想手动编辑配置文件，请参考以下格式：

```json
{
  "smsbao": {
    "username": "your_username",
    "password": "9b11127a9701975c734b8aee81ee3526",
    "api_key": "",
    "goods_id": "",
    "signature": "【重庆永柠科技】",
    "api_url": "https://api.smsbao.com/sms",
    "query_url": "https://api.smsbao.com/query",
    "enabled": true
  }
}
```

## 🧪 测试功能

### 配置验证
```bash
python sms_config_helper.py
```
选择"2. 查看当前配置"检查配置状态

### 功能验证
短信功能已集成到注册流程中，当用户选择手机号注册时会自动发送真实短信验证码。

## 🔄 工作流程

1. **用户注册/登录** → 选择手机号验证
2. **系统生成验证码** → 6位随机数字
3. **调用短信宝API** → 发送格式化短信
4. **用户输入验证码** → 系统验证有效性

## 🛡️ 安全特性

- ✅ 验证码5分钟有效期
- ✅ 使用后自动失效
- ✅ 手机号格式验证
- ✅ 发送频率限制
- ✅ 错误处理和回退机制

## 📞 故障排除

### 短信发送失败
1. 检查账户余额是否充足
2. 验证用户名和密码/ApiKey是否正确
3. 确认手机号格式正确
4. 检查短信内容是否包含敏感词

### 配置问题
1. 运行 `python test_sms.py` 查看详细错误信息
2. 检查 `data/sms_config.json` 文件格式
3. 确认所有必填字段已填写

### 常见错误代码
- `30`: 错误密码
- `40`: 账号不存在
- `41`: 余额不足
- `43`: IP地址限制
- `50`: 内容含有敏感词
- `51`: 手机号码不正确

## 📈 使用建议

1. **测试阶段**: 使用单个手机号测试，避免频繁发送
2. **生产环境**: 建议在短信宝后台报备短信模板
3. **监控**: 定期检查账户余额和发送状态
4. **备份**: 保持模拟发送作为备用方案

## 🎯 注意事项

- 相同号码一天默认只能接收4条短信
- 群发短信需要人工审核
- 建议使用ApiKey而不是密码MD5
- 短信内容必须正规，不能发送测试性质内容

---

配置完成后，您的柠汐认证平台将支持真实的短信验证码发送功能！🎉
