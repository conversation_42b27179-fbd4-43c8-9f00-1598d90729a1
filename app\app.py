from flask import Flask, send_from_directory, send_file
from flask_cors import CORS
import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from auth import auth_bp

def create_app():
    app = Flask(__name__)

    # 设置密钥用于session
    app.secret_key = 'ningxi_auth_platform_secret_key_2024'

    # 启用CORS支持
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:5000", "http://127.0.0.1:5000"],
            "methods": ["GET", "POST", "PUT", "DELETE"],
            "allow_headers": ["Content-Type", "Authorization"],
            "supports_credentials": True
        }
    })
    
    # 注册蓝图
    app.register_blueprint(auth_bp)
    
    # 静态文件路由
    @app.route('/')
    def index():
        """登录页面路由"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_file(os.path.join(frontend_path, 'index.html'))

    @app.route('/register.html')
    def register():
        """注册页面路由"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_file(os.path.join(frontend_path, 'register.html'))

    @app.route('/dashboard.html')
    def dashboard():
        """用户中心页面路由"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_file(os.path.join(frontend_path, 'dashboard.html'))

    @app.route('/index.html')
    def index_html():
        """登录页面路由（index.html）"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_file(os.path.join(frontend_path, 'index.html'))

    @app.route('/qr-demo.html')
    def qr_demo():
        """二维码登录演示页面"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_file(os.path.join(frontend_path, 'qr-demo.html'))
    
    @app.route('/css/<path:filename>')
    def css_files(filename):
        """CSS文件路由"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_from_directory(os.path.join(frontend_path, 'css'), filename)
    
    @app.route('/js/<path:filename>')
    def js_files(filename):
        """JavaScript文件路由"""
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
        return send_from_directory(os.path.join(frontend_path, 'js'), filename)
    
    @app.route('/health')
    def health_check():
        """健康检查接口"""
        return {
            'status': 'healthy',
            'message': '柠汐认证平台运行正常'
        }
    
    return app

if __name__ == '__main__':
    app = create_app()
    print("🍋 柠汐认证平台启动中...")
    print("📍 访问地址: http://localhost:5000")
    print("🔧 API文档: http://localhost:5000/health")
    app.run(debug=True, host='0.0.0.0', port=5000)
