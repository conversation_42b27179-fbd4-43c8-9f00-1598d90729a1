from flask import Blueprint, request, jsonify, session
import json
from datetime import datetime, timedelta
from utils import UserManager, VerificationCodeManager, validate_input

auth_bp = Blueprint('auth', __name__)
user_manager = UserManager()
verification_manager = VerificationCodeManager()

def get_current_user_id():
    """获取当前用户ID"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None

        # 从请求头获取用户信息
        user_info_header = request.headers.get('X-User-Info', '{}')
        if user_info_header == '{}':
            return None

        user_info = json.loads(user_info_header)
        return user_info.get('id')
    except Exception as e:
        print(f"获取用户ID失败: {e}")
        return None

def get_current_user():
    """获取当前用户完整信息"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None

        # 从请求头获取用户信息
        user_info_header = request.headers.get('X-User-Info', '{}')
        if user_info_header == '{}':
            return None

        user_info = json.loads(user_info_header)
        return user_info
    except Exception as e:
        print(f"获取用户信息失败: {e}")
        return None

def validate_client_credentials(client_id, client_secret):
    """验证第三方应用凭据"""
    try:
        # 从数据库查询客户端信息
        clients = user_manager.load_oauth_clients()

        # 检查客户端是否存在且状态为活跃
        if client_id in clients:
            client_info = clients[client_id]
            return (client_info.get('client_secret') == client_secret and
                   client_info.get('status') == 'active')

        # 兼容一些预设的测试客户端
        default_clients = {
            'demo_app': 'demo_secret_123',
            'test_client': 'test_secret_456',
            'my_app': 'my_secret_789',
        }

        return default_clients.get(client_id) == client_secret

    except Exception as e:
        print(f"验证客户端凭据失败: {e}")
        return False

@auth_bp.route('/api/send-code', methods=['POST'])
def send_verification_code():
    """发送验证码接口"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': '请提供有效的JSON数据'
            }), 400

        contact = data.get('contact', '').strip()
        contact_type = data.get('contact_type', 'email')

        if not contact:
            return jsonify({
                'success': False,
                'message': '请提供邮箱或手机号'
            }), 400

        # 验证格式
        if contact_type == 'email':
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, contact):
                return jsonify({
                    'success': False,
                    'message': '邮箱格式不正确'
                }), 400
        elif contact_type == 'phone':
            import re
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, contact):
                return jsonify({
                    'success': False,
                    'message': '手机号格式不正确'
                }), 400

        # 发送验证码
        success, message = verification_manager.send_code(contact, contact_type)

        if success:
            return jsonify({
                'success': True,
                'message': message
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@auth_bp.route('/api/register', methods=['POST'])
def register():
    """用户注册接口"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供有效的JSON数据'
            }), 400
        
        # 验证输入
        register_type = data.get('register_type', 'email')
        required_fields = ['username', 'password', 'verification_code']

        if register_type == 'email':
            required_fields.append('email')
        else:
            required_fields.append('phone')

        errors = validate_input(data, required_fields)

        if errors:
            return jsonify({
                'success': False,
                'message': '输入验证失败',
                'errors': errors
            }), 400

        # 验证验证码
        contact = data.get('email' if register_type == 'email' else 'phone', '').strip()
        verification_code = data.get('verification_code', '').strip()

        code_valid, code_message = verification_manager.verify_code(contact, verification_code)
        if not code_valid:
            return jsonify({
                'success': False,
                'message': code_message
            }), 400

        # 创建用户
        if register_type == 'email':
            success, message = user_manager.create_user(
                data['username'].strip(),
                data['password'],
                email=contact
            )
        else:
            success, message = user_manager.create_user(
                data['username'].strip(),
                data['password'],
                phone=contact
            )
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            }), 201
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 409
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@auth_bp.route('/api/login', methods=['POST'])
def login():
    """用户登录接口"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请提供有效的JSON数据'
            }), 400
        
        # 验证输入
        required_fields = ['username', 'password']
        errors = validate_input(data, required_fields)
        
        if errors:
            return jsonify({
                'success': False,
                'message': '输入验证失败',
                'errors': errors
            }), 400
        
        # 获取客户端信息
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR'))
        user_agent = request.headers.get('User-Agent')

        # 用户认证
        success, result = user_manager.authenticate_user(
            data['username'].strip(),
            data['password'],
            ip_address,
            user_agent
        )
        
        if success:
            # 生成永久认证令牌（不过期）
            import time
            auth_token = f"permanent_token_{result['id']}_{int(time.time())}"

            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': result,
                'auth_token': auth_token,
                'expires': 'never'  # 永不过期
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': result
            }), 401
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

@auth_bp.route('/api/users', methods=['GET'])
def get_users():
    """获取用户列表（仅用于测试，实际项目中应该有权限控制）"""
    try:
        users = user_manager.load_users()
        # 移除敏感信息
        safe_users = []
        for user in users:
            safe_user = {
                'id': user['id'],
                'username': user['username'],
                'email': user.get('email'),
                'phone': user.get('phone'),
                'register_type': user.get('register_type', 'email'),
                'created_at': user['created_at'],
                'last_login': user.get('last_login')
            }
            safe_users.append(safe_user)
        
        return jsonify({
            'success': True,
            'users': safe_users,
            'count': len(safe_users)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500

# 用户资料相关API
@auth_bp.route('/api/user/profile', methods=['GET'])
def get_user_profile():
    """获取用户资料"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'success': False, 'message': '未授权访问'}), 401

    token = auth_header.split(' ')[1]

    # 从localStorage获取的用户信息中提取用户ID
    # 这里我们简化验证，实际项目中应该有更安全的token验证机制
    try:
        # 从请求头获取用户信息
        user_info_header = request.headers.get('X-User-Info', '{}')
        print(f"收到的用户信息头: {user_info_header}")

        if user_info_header == '{}':
            return jsonify({'success': False, 'message': '缺少用户信息'}), 401

        user_info = json.loads(user_info_header)
        user_id = user_info.get('id')
        print(f"提取的用户ID: {user_id}")

        if not user_id:
            return jsonify({'success': False, 'message': '无效的用户ID'}), 401
    except Exception as e:
        print(f"解析用户信息失败: {e}")
        return jsonify({'success': False, 'message': f'令牌格式错误: {str(e)}'}), 401

    try:
        users = user_manager.load_users()
        user = next((u for u in users if u['id'] == user_id), None)

        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 返回用户信息（不包含密码）
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'email': user.get('email'),
            'phone': user.get('phone'),
            'created_at': user['created_at'],
            'last_login': user.get('last_login'),
            'register_type': user.get('register_type', 'email')
        }

        return jsonify(user_info)

    except Exception as e:
        print(f"获取用户资料失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/user/profile', methods=['PUT'])
def update_user_profile():
    """更新用户资料"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'success': False, 'message': '未授权访问'}), 401

    token = auth_header.split(' ')[1]

    # 从请求头获取用户信息
    try:
        user_info = json.loads(request.headers.get('X-User-Info', '{}'))
        user_id = user_info.get('id')

        if not user_id:
            return jsonify({'success': False, 'message': '无效的令牌'}), 401
    except:
        return jsonify({'success': False, 'message': '令牌格式错误'}), 401

    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        phone = data.get('phone', '').strip()

        users = user_manager.load_users()
        user_index = next((i for i, u in enumerate(users) if u['id'] == user_id), None)

        if user_index is None:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 检查邮箱是否已被其他用户使用
        if email and any(u.get('email') == email and u['id'] != user_id for u in users):
            return jsonify({'success': False, 'message': '该邮箱已被其他用户使用'}), 400

        # 检查手机号是否已被其他用户使用
        if phone and any(u.get('phone') == phone and u['id'] != user_id for u in users):
            return jsonify({'success': False, 'message': '该手机号已被其他用户使用'}), 400

        # 更新用户信息
        if email:
            users[user_index]['email'] = email
        if phone:
            users[user_index]['phone'] = phone

        user_manager.save_users(users)

        return jsonify({'success': True, 'message': '资料更新成功'})

    except Exception as e:
        print(f"更新用户资料失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/user/bind', methods=['POST'])
def bind_contact():
    """绑定联系方式"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'success': False, 'message': '未授权访问'}), 401

    token = auth_header.split(' ')[1]

    # 从请求头获取用户信息
    try:
        user_info = json.loads(request.headers.get('X-User-Info', '{}'))
        user_id = user_info.get('id')

        if not user_id:
            return jsonify({'success': False, 'message': '无效的令牌'}), 401
    except:
        return jsonify({'success': False, 'message': '令牌格式错误'}), 401

    try:
        data = request.get_json()
        bind_type = data.get('type')  # 'email' 或 'phone'
        contact = data.get('contact', '').strip()
        code = data.get('code', '').strip()

        if not all([bind_type, contact, code]):
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        # 验证验证码
        if not verification_manager.verify_code(contact, code):
            return jsonify({'success': False, 'message': '验证码错误或已过期'}), 400

        users = user_manager.load_users()
        user_index = next((i for i, u in enumerate(users) if u['id'] == user_id), None)

        if user_index is None:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 检查联系方式是否已被其他用户使用
        if bind_type == 'email':
            if any(u.get('email') == contact and u['id'] != user_id for u in users):
                return jsonify({'success': False, 'message': '该邮箱已被其他用户使用'}), 400
            users[user_index]['email'] = contact
        elif bind_type == 'phone':
            if any(u.get('phone') == contact and u['id'] != user_id for u in users):
                return jsonify({'success': False, 'message': '该手机号已被其他用户使用'}), 400
            users[user_index]['phone'] = contact
        else:
            return jsonify({'success': False, 'message': '无效的绑定类型'}), 400

        user_manager.save_users(users)

        return jsonify({'success': True, 'message': '绑定成功'})

    except Exception as e:
        print(f"绑定联系方式失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/user/change_password', methods=['POST'])
def change_password():
    """修改密码"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'success': False, 'message': '未授权访问'}), 401

    token = auth_header.split(' ')[1]

    # 从请求头获取用户信息
    try:
        user_info = json.loads(request.headers.get('X-User-Info', '{}'))
        user_id = user_info.get('id')

        if not user_id:
            return jsonify({'success': False, 'message': '无效的令牌'}), 401
    except:
        return jsonify({'success': False, 'message': '令牌格式错误'}), 401

    try:
        data = request.get_json()
        current_password = data.get('current_password', '').strip()
        new_password = data.get('new_password', '').strip()

        if not all([current_password, new_password]):
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '新密码长度至少6位'}), 400

        users = user_manager.load_users()
        user_index = next((i for i, u in enumerate(users) if u['id'] == user_id), None)

        if user_index is None:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 验证当前密码
        if users[user_index]['password'] != current_password:
            return jsonify({'success': False, 'message': '当前密码错误'}), 400

        # 更新密码
        users[user_index]['password'] = new_password
        user_manager.save_users(users)

        return jsonify({'success': True, 'message': '密码修改成功'})

    except Exception as e:
        print(f"修改密码失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/user/login_history', methods=['GET'])
def get_login_history():
    """获取用户登录历史"""
    try:
        # 验证用户身份
        user_id = get_current_user_id()
        if not user_id:
            return jsonify({'success': False, 'message': '未授权访问'}), 401

        # 获取登录历史
        history = user_manager.get_login_history(user_id)

        return jsonify({
            'success': True,
            'data': history,
            'message': '获取登录历史成功'
        })

    except Exception as e:
        print(f"获取登录历史失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/oauth/authorize', methods=['POST'])
def oauth_authorize():
    """开放API：第三方应用请求授权URL"""
    try:
        data = request.get_json()

        # 验证必需参数
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        callback_url = data.get('callback_url')
        app_name = data.get('app_name', '第三方应用')

        if not all([client_id, client_secret, callback_url]):
            return jsonify({
                'success': False,
                'message': '缺少必需参数：client_id, client_secret, callback_url'
            }), 400

        # 验证第三方应用（简单验证，实际项目中应该有完整的应用注册系统）
        if not validate_client_credentials(client_id, client_secret):
            return jsonify({
                'success': False,
                'message': '无效的客户端凭据'
            }), 401

        # 生成请求ID
        import uuid
        request_id = str(uuid.uuid4())

        # 存储请求信息
        qr_requests = user_manager.load_qr_requests()
        qr_requests[request_id] = {
            'client_id': client_id,
            'app_name': app_name,
            'callback_url': callback_url,
            'created_at': datetime.now().isoformat(),
            'status': 'pending',
            'expires_at': (datetime.now() + timedelta(minutes=10)).isoformat()  # 10分钟有效期
        }
        user_manager.save_qr_requests(qr_requests)

        # 生成授权URL
        auth_url = f"{request.host_url}qr-auth.html?request_id={request_id}&client_id={client_id}&app_name={app_name}"

        return jsonify({
            'success': True,
            'request_id': request_id,
            'auth_url': auth_url,  # 第三方应用将此URL生成二维码
            'expires_in': 600,  # 10分钟
            'polling_url': f"{request.host_url}api/oauth/status/{request_id}",  # 轮询状态的URL
            'message': '请将auth_url生成二维码供用户扫描'
        })

    except Exception as e:
        print(f"生成授权URL失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/qr/generate', methods=['POST'])
def generate_qr_login():
    """内部演示：生成二维码登录请求"""
    try:
        data = request.get_json()
        client_id = data.get('client_id', 'demo_client')
        callback_url = data.get('callback_url')

        if not callback_url:
            return jsonify({'success': False, 'message': '缺少回调URL'}), 400

        # 生成请求ID
        import uuid
        request_id = str(uuid.uuid4())

        # 存储请求信息
        qr_requests = user_manager.load_qr_requests()
        qr_requests[request_id] = {
            'client_id': client_id,
            'app_name': '演示应用',
            'callback_url': callback_url,
            'created_at': datetime.now().isoformat(),
            'status': 'pending',
            'expires_at': (datetime.now() + timedelta(minutes=5)).isoformat()
        }
        user_manager.save_qr_requests(qr_requests)

        # 生成二维码URL
        auth_url = f"{request.host_url}qr-auth.html?request_id={request_id}&client_id={client_id}&app_name=演示应用"

        return jsonify({
            'success': True,
            'request_id': request_id,
            'qr_url': auth_url,
            'expires_in': 300
        })

    except Exception as e:
        print(f"生成二维码失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/qr/authorize', methods=['POST'])
def authorize_qr_login():
    """授权二维码登录"""
    try:
        # 验证用户身份
        user = get_current_user()
        if not user:
            return jsonify({'success': False, 'message': '未授权访问'}), 401

        # 检查用户是否已绑定手机号和邮箱
        if not user.get('email') or not user.get('phone'):
            return jsonify({'success': False, 'message': '请先绑定手机号和邮箱后再进行授权'}), 400

        data = request.get_json()
        request_id = data.get('request_id')
        authorize = data.get('authorize', False)

        if not request_id:
            return jsonify({'success': False, 'message': '缺少请求ID'}), 400

        # 获取请求信息
        qr_requests = user_manager.load_qr_requests()
        if request_id not in qr_requests:
            return jsonify({'success': False, 'message': '无效的请求ID'}), 404

        request_info = qr_requests[request_id]

        # 检查是否过期
        if datetime.now() > datetime.fromisoformat(request_info['expires_at']):
            return jsonify({'success': False, 'message': '请求已过期'}), 400

        if authorize:
            # 更新请求状态
            request_info['status'] = 'authorized'
            request_info['user_data'] = {
                'user_id': user['id'],
                'username': user['username'],
                'email': user.get('email'),
                'phone': user.get('phone')
            }
            request_info['authorized_at'] = datetime.now().isoformat()

            qr_requests[request_id] = request_info
            user_manager.save_qr_requests(qr_requests)

            return jsonify({'success': True, 'message': '授权成功'})
        else:
            # 拒绝授权
            request_info['status'] = 'denied'
            qr_requests[request_id] = request_info
            user_manager.save_qr_requests(qr_requests)

            return jsonify({'success': True, 'message': '已拒绝授权'})

    except Exception as e:
        print(f"授权失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/oauth/status/<request_id>', methods=['GET'])
def oauth_check_status(request_id):
    """开放API：检查授权状态并返回用户数据"""
    try:
        qr_requests = user_manager.load_qr_requests()

        if request_id not in qr_requests:
            return jsonify({'success': False, 'message': '无效的请求ID'}), 404

        request_info = qr_requests[request_id]

        # 检查是否过期
        if datetime.now() > datetime.fromisoformat(request_info['expires_at']):
            request_info['status'] = 'expired'
            qr_requests[request_id] = request_info
            user_manager.save_qr_requests(qr_requests)

        response_data = {
            'success': True,
            'status': request_info['status'],
            'request_id': request_id,
            'client_id': request_info.get('client_id'),
            'app_name': request_info.get('app_name')
        }

        # 如果已授权，返回完整用户数据
        if request_info['status'] == 'authorized':
            user_data = request_info.get('user_data', {})
            response_data['user'] = {
                'user_id': user_data.get('user_id'),
                'username': user_data.get('username'),
                'email': user_data.get('email'),
                'phone': user_data.get('phone'),
                'authorized_at': request_info.get('authorized_at')
            }

            # 授权成功后，可以选择删除请求记录（一次性使用）
            # del qr_requests[request_id]
            # user_manager.save_qr_requests(qr_requests)

        return jsonify(response_data)

    except Exception as e:
        print(f"检查状态失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/qr/status/<request_id>', methods=['GET'])
def check_qr_status(request_id):
    """内部演示：检查二维码状态"""
    try:
        qr_requests = user_manager.load_qr_requests()

        if request_id not in qr_requests:
            return jsonify({'success': False, 'message': '无效的请求ID'}), 404

        request_info = qr_requests[request_id]

        # 检查是否过期
        if datetime.now() > datetime.fromisoformat(request_info['expires_at']):
            request_info['status'] = 'expired'
            qr_requests[request_id] = request_info
            user_manager.save_qr_requests(qr_requests)

        response_data = {
            'success': True,
            'status': request_info['status']
        }

        # 如果已授权，返回用户数据
        if request_info['status'] == 'authorized':
            response_data['user_data'] = request_info.get('user_data')

        return jsonify(response_data)

    except Exception as e:
        print(f"检查状态失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/oauth/register', methods=['POST'])
def oauth_register_client():
    """开放API：注册第三方应用"""
    try:
        data = request.get_json()

        app_name = data.get('app_name')
        app_description = data.get('app_description', '')
        callback_urls = data.get('callback_urls', [])
        contact_email = data.get('contact_email')

        if not all([app_name, contact_email]):
            return jsonify({
                'success': False,
                'message': '缺少必需参数：app_name, contact_email'
            }), 400

        # 生成客户端凭据
        import uuid
        import secrets
        client_id = f"app_{uuid.uuid4().hex[:16]}"
        client_secret = secrets.token_urlsafe(32)

        # 保存客户端信息（实际项目中应保存到数据库）
        clients = user_manager.load_oauth_clients()
        clients[client_id] = {
            'client_secret': client_secret,
            'app_name': app_name,
            'app_description': app_description,
            'callback_urls': callback_urls,
            'contact_email': contact_email,
            'created_at': datetime.now().isoformat(),
            'status': 'active'
        }
        user_manager.save_oauth_clients(clients)

        return jsonify({
            'success': True,
            'client_id': client_id,
            'client_secret': client_secret,
            'message': '应用注册成功，请妥善保管client_secret'
        })

    except Exception as e:
        print(f"注册客户端失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500

@auth_bp.route('/api/oauth/docs', methods=['GET'])
def oauth_docs():
    """开放API：API文档"""
    docs = {
        'title': '柠汐认证平台 OAuth API',
        'version': '1.0.0',
        'description': '第三方应用集成扫码登录API',
        'base_url': request.host_url + 'api/oauth/',
        'endpoints': {
            'register': {
                'url': '/api/oauth/register',
                'method': 'POST',
                'description': '注册第三方应用',
                'parameters': {
                    'app_name': '应用名称（必需）',
                    'app_description': '应用描述（可选）',
                    'callback_urls': '回调URL列表（可选）',
                    'contact_email': '联系邮箱（必需）'
                },
                'response': {
                    'client_id': '客户端ID',
                    'client_secret': '客户端密钥'
                }
            },
            'authorize': {
                'url': '/api/oauth/authorize',
                'method': 'POST',
                'description': '请求用户授权',
                'parameters': {
                    'client_id': '客户端ID（必需）',
                    'client_secret': '客户端密钥（必需）',
                    'callback_url': '授权后回调URL（必需）',
                    'app_name': '应用名称（可选）'
                },
                'response': {
                    'request_id': '请求ID',
                    'auth_url': '授权URL（生成二维码）',
                    'polling_url': '状态查询URL',
                    'expires_in': '有效期（秒）'
                }
            },
            'status': {
                'url': '/api/oauth/status/{request_id}',
                'method': 'GET',
                'description': '查询授权状态',
                'response': {
                    'status': 'pending/authorized/denied/expired',
                    'user': '用户信息（授权成功时返回）'
                }
            }
        },
        'flow': [
            '1. 调用 /api/oauth/register 注册应用获取凭据',
            '2. 调用 /api/oauth/authorize 获取授权URL',
            '3. 将授权URL生成二维码供用户扫描',
            '4. 轮询 /api/oauth/status/{request_id} 查询状态',
            '5. 用户扫码授权后获取用户信息'
        ]
    }

    return jsonify(docs)

@auth_bp.route('/api/set_session', methods=['POST'])
def set_session():
    """设置用户会话"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        auth_token = data.get('auth_token')

        if not all([user_id, auth_token]):
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        # 设置session
        session['user_id'] = user_id
        session['auth_token'] = auth_token

        return jsonify({'success': True, 'message': '会话设置成功'})

    except Exception as e:
        print(f"设置会话失败: {e}")
        return jsonify({'success': False, 'message': '服务器错误'}), 500
