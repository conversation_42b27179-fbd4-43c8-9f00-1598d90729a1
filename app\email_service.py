#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送服务模块
支持阿里云邮件推送服务
"""

import json
import os
import time
import uuid
import hashlib
import hmac
import base64
import urllib.parse
import urllib.request
from datetime import datetime


class EmailService:
    """邮件发送服务类"""
    
    def __init__(self):
        self.config = self._load_config()
        self.aliyun_config = self.config.get('aliyun_dm', {})
    
    def _load_config(self):
        """加载邮件配置"""
        try:
            config_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'email_config.json')
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"📧 邮件配置加载失败: {e}")
            return {}
    
    def _validate_email(self, email):
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _generate_signature(self, params, method='GET'):
        """生成阿里云API签名"""
        try:
            access_key_secret = self.aliyun_config.get('access_key_secret', '')
            
            # 1. 排序参数
            sorted_params = sorted(params.items())
            
            # 2. 构造规范化查询字符串
            canonicalized_query_string = '&'.join([
                f"{self._percent_encode(k)}={self._percent_encode(str(v))}"
                for k, v in sorted_params
            ])
            
            # 3. 构造待签名字符串
            string_to_sign = (
                f"{method}&"
                f"{self._percent_encode('/')}&"
                f"{self._percent_encode(canonicalized_query_string)}"
            )
            
            # 4. 计算签名
            key = f"{access_key_secret}&"
            signature = hmac.new(
                key.encode('utf-8'),
                string_to_sign.encode('utf-8'),
                hashlib.sha1
            ).digest()
            
            # 5. Base64编码
            return base64.b64encode(signature).decode('utf-8')
            
        except Exception as e:
            print(f"📧 签名生成失败: {e}")
            return ""
    
    def _percent_encode(self, s):
        """URL编码"""
        return urllib.parse.quote(str(s), safe='~')
    
    def _build_email_content(self, email, code, template_name='verification_code'):
        """构建邮件内容"""
        templates = self.aliyun_config.get('templates', {})
        template = templates.get(template_name, {})
        
        subject = template.get('subject', '【柠汐认证】邮箱验证码')
        html_body = template.get('html_body', '您的验证码是：{code}')
        text_body = template.get('text_body', '您的验证码是：{code}')
        
        # 替换验证码
        html_body = html_body.replace('{code}', code)
        text_body = text_body.replace('{code}', code)
        
        return subject, html_body, text_body
    
    def send_verification_code(self, email, code):
        """发送邮箱验证码"""
        try:
            # 检查是否启用
            if not self.aliyun_config.get('enabled', False):
                print("📧 邮件服务未启用")
                return False, "邮件服务未启用"
            
            # 验证邮箱格式
            if not self._validate_email(email):
                print(f"📧 邮箱格式错误: {email}")
                return False, "邮箱格式错误"
            
            # 检查必要配置
            required_fields = ['access_key_id', 'access_key_secret', 'account_name']
            for field in required_fields:
                if not self.aliyun_config.get(field):
                    print(f"📧 邮件配置不完整，缺少: {field}")
                    return False, "邮件配置不完整"
            
            print(f"📧 正在发送邮件到 {email}")
            
            # 构建邮件内容
            subject, html_body, text_body = self._build_email_content(email, code)
            
            # 构建请求参数
            params = {
                'Action': 'SingleSendMail',
                'AccountName': self.aliyun_config.get('account_name'),
                'ReplyToAddress': self.aliyun_config.get('reply_to_address', False),
                'AddressType': 1,
                'ToAddress': email,
                'Subject': subject,
                'HtmlBody': html_body,
                'TextBody': text_body,
                'FromAlias': self.aliyun_config.get('from_alias', '柠汐认证平台'),
                'ClickTrace': self.aliyun_config.get('click_trace', '0'),
                'Format': 'JSON',
                'Version': '2015-11-23',
                'AccessKeyId': self.aliyun_config.get('access_key_id'),
                'SignatureMethod': 'HMAC-SHA1',
                'Timestamp': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),
                'SignatureVersion': '1.0',
                'SignatureNonce': str(uuid.uuid4()),
                'RegionId': self.aliyun_config.get('region_id', 'cn-hangzhou')
            }
            
            # 生成签名
            signature = self._generate_signature(params, 'GET')
            params['Signature'] = signature
            
            # 构建请求URL
            endpoint = self.aliyun_config.get('endpoint', 'dm.aliyuncs.com')
            query_string = '&'.join([f"{k}={urllib.parse.quote(str(v))}" for k, v in params.items()])
            url = f"https://{endpoint}/?{query_string}"
            
            # 发送请求
            try:
                # 禁用代理（类似短信发送的处理方式）
                old_proxies = {}
                for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
                    if key in os.environ:
                        old_proxies[key] = os.environ[key]
                        del os.environ[key]
                
                try:
                    proxy_handler = urllib.request.ProxyHandler({})
                    opener = urllib.request.build_opener(proxy_handler)
                    
                    # 创建SSL上下文
                    import ssl
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE
                    
                    https_handler = urllib.request.HTTPSHandler(context=ssl_context)
                    opener.add_handler(https_handler)
                    
                    request = urllib.request.Request(url)
                    response = opener.open(request, timeout=15)
                    result = response.read().decode('utf-8')
                    
                finally:
                    # 恢复代理设置
                    for key, value in old_proxies.items():
                        os.environ[key] = value
                
                # 解析响应
                try:
                    result_data = json.loads(result)
                    if 'RequestId' in result_data:
                        print(f"📧 邮件发送成功")
                        return True, "验证码已发送到您的邮箱"
                    else:
                        error_code = result_data.get('Code', 'Unknown')
                        error_message = result_data.get('Message', '未知错误')
                        print(f"📧 邮件发送失败: {error_code} - {error_message}")
                        return False, f"邮件发送失败: {error_message}"
                except json.JSONDecodeError:
                    print(f"📧 响应解析失败: {result}")
                    return False, "邮件发送失败"
                
            except Exception as e:
                print(f"📧 邮件发送异常: {e}")
                return False, "邮件发送失败"
                
        except Exception as e:
            print(f"📧 邮件服务异常: {e}")
            return False, "邮件服务异常"
    
    def test_connection(self):
        """测试邮件服务连接"""
        try:
            if not self.aliyun_config.get('enabled', False):
                return False, "邮件服务未启用"
            
            # 检查配置完整性
            required_fields = ['access_key_id', 'access_key_secret', 'account_name']
            missing_fields = [field for field in required_fields if not self.aliyun_config.get(field)]
            
            if missing_fields:
                return False, f"配置不完整，缺少: {', '.join(missing_fields)}"
            
            return True, "邮件服务配置正常"
            
        except Exception as e:
            return False, f"配置检查失败: {e}"


# 测试函数
def test_email_service():
    """测试邮件服务"""
    service = EmailService()
    
    print("📧 邮件服务测试")
    print("=" * 40)
    
    # 测试连接
    success, message = service.test_connection()
    print(f"连接测试: {'✅' if success else '❌'} {message}")
    
    if success:
        # 测试发送（使用测试邮箱）
        test_email = "<EMAIL>"
        test_code = "123456"
        
        print(f"\n测试发送邮件到: {test_email}")
        success, message = service.send_verification_code(test_email, test_code)
        print(f"发送结果: {'✅' if success else '❌'} {message}")


if __name__ == '__main__':
    test_email_service()
