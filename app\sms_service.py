#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信服务模块 - 集成短信宝API
"""

import json
import os
import hashlib
import urllib.parse
import urllib.request
import urllib.error
from datetime import datetime, timedelta
import logging

class SMSService:
    """短信服务类"""
    
    def __init__(self):
        self.config_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'sms_config.json')
        self.config = self.load_config()
        self.logger = logging.getLogger(__name__)
    
    def load_config(self):
        """加载短信配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"配置文件不存在: {self.config_file}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            return {}
    
    def is_enabled(self):
        """检查短信服务是否启用"""
        return self.config.get('smsbao', {}).get('enabled', False)
    
    def validate_config(self):
        """验证配置是否完整"""
        smsbao_config = self.config.get('smsbao', {})
        
        if not smsbao_config.get('username'):
            return False, "缺少短信宝用户名"
        
        if not smsbao_config.get('password') and not smsbao_config.get('api_key'):
            return False, "缺少短信宝密码或ApiKey"
        
        return True, "配置验证通过"
    
    def format_phone(self, phone):
        """格式化手机号"""
        # 移除所有非数字字符
        phone = ''.join(filter(str.isdigit, phone))
        
        # 验证手机号格式
        if len(phone) == 11 and phone.startswith('1'):
            return phone
        elif len(phone) == 13 and phone.startswith('86'):
            return phone[2:]  # 移除国家代码
        else:
            return None
    
    def build_sms_content(self, template_name, **kwargs):
        """构建短信内容"""
        templates = self.config.get('sms_templates', {})
        template = templates.get(template_name, {})
        
        if not template:
            return None
        
        content = template.get('content', '')
        signature = self.config.get('smsbao', {}).get('signature', '【重庆永柠科技】')
        
        # 格式化内容
        try:
            formatted_content = content.format(**kwargs)
            return f"{signature}{formatted_content}"
        except KeyError as e:
            self.logger.error(f"短信模板参数缺失: {e}")
            return None
    
    def send_sms(self, phone, content):
        """发送短信"""
        if not self.is_enabled():
            self.logger.info("短信服务未启用，使用模拟发送")
            return True, "模拟发送成功"
        
        # 验证配置
        is_valid, message = self.validate_config()
        if not is_valid:
            self.logger.error(f"短信配置验证失败: {message}")
            return False, message
        
        # 格式化手机号
        formatted_phone = self.format_phone(phone)
        if not formatted_phone:
            return False, "手机号格式不正确"
        
        # 构建请求参数
        smsbao_config = self.config.get('smsbao', {})
        params = {
            'u': smsbao_config.get('username'),
            'm': formatted_phone,
            'c': content
        }
        
        # 添加密码或ApiKey
        if smsbao_config.get('api_key'):
            params['p'] = smsbao_config.get('api_key')
        else:
            params['p'] = smsbao_config.get('password')
        
        # 添加产品ID（如果有）
        if smsbao_config.get('goods_id'):
            params['g'] = smsbao_config.get('goods_id')
        
        # URL编码内容
        params['c'] = urllib.parse.quote(content.encode('utf-8'))
        
        # 构建请求URL
        api_url = smsbao_config.get('api_url', 'https://api.smsbao.com/sms')
        query_string = urllib.parse.urlencode(params)
        full_url = f"{api_url}?{query_string}"
        
        try:
            # 发送请求
            self.logger.info(f"发送短信到 {formatted_phone}")
            with urllib.request.urlopen(full_url, timeout=10) as response:
                result = response.read().decode('utf-8').strip()
            
            # 解析结果
            if result == '0':
                self.logger.info(f"短信发送成功: {formatted_phone}")
                return True, "发送成功"
            else:
                error_msg = self.get_error_message(result)
                self.logger.error(f"短信发送失败: {formatted_phone}, 错误: {error_msg}")
                return False, error_msg
                
        except urllib.error.URLError as e:
            self.logger.error(f"短信发送网络错误: {e}")
            return False, "网络连接失败"
        except Exception as e:
            self.logger.error(f"短信发送异常: {e}")
            return False, "发送失败"
    
    def get_error_message(self, error_code):
        """获取错误信息"""
        error_codes = self.config.get('error_codes', {})
        return error_codes.get(str(error_code), f"未知错误代码: {error_code}")
    
    def send_verification_code(self, phone, code, user_name="用户", expire_minutes=5):
        """发送验证码短信"""
        content = self.build_sms_content(
            'verification_code',
            user_name=user_name,
            code=code,
            time=f"{expire_minutes}分钟"
        )
        
        if not content:
            return False, "短信模板构建失败"
        
        return self.send_sms(phone, content)
    
    def query_balance(self):
        """查询账户余额"""
        if not self.is_enabled():
            return False, "短信服务未启用"
        
        # 验证配置
        is_valid, message = self.validate_config()
        if not is_valid:
            return False, message
        
        smsbao_config = self.config.get('smsbao', {})
        params = {
            'u': smsbao_config.get('username')
        }
        
        # 添加密码或ApiKey
        if smsbao_config.get('api_key'):
            params['p'] = smsbao_config.get('api_key')
        else:
            params['p'] = smsbao_config.get('password')
        
        # 构建请求URL
        query_url = smsbao_config.get('query_url', 'https://api.smsbao.com/query')
        query_string = urllib.parse.urlencode(params)
        full_url = f"{query_url}?{query_string}"
        
        try:
            with urllib.request.urlopen(full_url, timeout=10) as response:
                result = response.read().decode('utf-8').strip()
            
            lines = result.split('\n')
            if lines[0] == '0' and len(lines) > 1:
                balance_info = lines[1].split(',')
                if len(balance_info) == 2:
                    sent_count, remaining_count = balance_info
                    return True, {
                        'sent_count': int(sent_count),
                        'remaining_count': int(remaining_count)
                    }
            
            error_msg = self.get_error_message(lines[0])
            return False, error_msg
            
        except Exception as e:
            self.logger.error(f"查询余额失败: {e}")
            return False, "查询失败"


# 全局短信服务实例
sms_service = SMSService()
