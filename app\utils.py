import json
import os
from datetime import datetime, timedelta
import uuid
import random
import re
# 短信服务和邮件服务将在需要时动态导入

class VerificationCodeManager:
    def __init__(self, data_file='data/verification_codes.json'):
        self.data_file = data_file
        self.ensure_data_file()

    def ensure_data_file(self):
        """确保验证码数据文件存在"""
        if not os.path.exists(self.data_file):
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump({"codes": []}, f, ensure_ascii=False, indent=2)

    def load_codes(self):
        """加载验证码数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('codes', [])
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def save_codes(self, codes):
        """保存验证码数据"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump({"codes": codes}, f, ensure_ascii=False, indent=2)

    def generate_code(self):
        """生成6位数字验证码"""
        return str(random.randint(100000, 999999))

    def send_code(self, contact, contact_type='email'):
        """发送验证码（模拟）"""
        code = self.generate_code()
        codes = self.load_codes()

        # 清理过期的验证码
        current_time = datetime.now()
        codes = [c for c in codes if datetime.fromisoformat(c['expires_at']) > current_time]

        # 添加新验证码
        new_code = {
            "id": str(uuid.uuid4()),
            "contact": contact,
            "contact_type": contact_type,
            "code": code,
            "created_at": current_time.isoformat(),
            "expires_at": (current_time + timedelta(minutes=5)).isoformat(),
            "used": False
        }

        codes.append(new_code)
        self.save_codes(codes)

        # 发送验证码
        if contact_type == 'email':
            # 使用阿里云邮件推送服务发送真实邮件
            success, message = self._send_real_email(contact, code)
            if success:
                print(f"📧 邮件发送成功到 {contact}: {code}")
                return True, message
            else:
                # 如果邮件发送失败，回退到模拟发送
                print(f"📧 回退到模拟发送邮件到 {contact}: {code}")
                return True, f"验证码已发送到您的邮箱（模拟）"
        else:
            # 使用短信宝发送真实短信
            success = self._send_real_sms(contact, code)
            if success:
                print(f"📱 短信发送成功到 {contact}: {code}")
                return True, f"验证码已发送到您的手机"
            else:
                # 如果短信发送失败，回退到模拟发送
                print(f"📱 回退到模拟发送短信到 {contact}: {code}")
                return True, f"验证码已发送到您的手机（模拟）"

    def _send_real_sms(self, phone, code):
        """发送真实短信"""
        try:
            import os

            # 加载配置
            config_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'sms_config.json')
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            smsbao = config.get('smsbao', {})

            # 检查是否启用
            if not smsbao.get('enabled', False):
                print("📱 短信服务未启用")
                return False

            # 检查配置
            username = smsbao.get('username')
            password = smsbao.get('password') or smsbao.get('api_key')

            if not username or not password:
                print("📱 短信配置不完整")
                return False

            # 构建短信内容
            signature = smsbao.get('signature', '【重庆永柠科技】')
            content = f"{signature}亲爱的用户，感谢您选择柠汐认证，您的验证码是{code}。有效期为5分钟，请尽快验证！"

            print(f"📱 正在发送短信到 {phone}")

            # 方法1: 尝试使用requests库（推荐）
            result = None
            try:
                import requests

                # 禁用代理和SSL验证
                proxies = {'http': None, 'https': None}
                params = {'u': username, 'p': password, 'm': phone, 'c': content}

                # 先尝试HTTPS
                try:
                    response = requests.get('https://api.smsbao.com/sms',
                                          params=params, timeout=15, verify=False, proxies=proxies)
                    result = response.text.strip()
                    pass
                except:
                    # HTTPS失败，尝试HTTP
                    response = requests.get('http://api.smsbao.com/sms',
                                          params=params, timeout=15, proxies=proxies)
                    result = response.text.strip()

            except ImportError:
                pass

            # 方法2: 如果requests失败或不可用，使用urllib
            if result is None:
                import urllib.parse
                import urllib.request

                # 禁用代理
                old_proxies = {}
                for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
                    if key in os.environ:
                        old_proxies[key] = os.environ[key]
                        del os.environ[key]

                try:
                    proxy_handler = urllib.request.ProxyHandler({})
                    opener = urllib.request.build_opener(proxy_handler)

                    encoded_content = urllib.parse.quote(content.encode('utf-8'))
                    url = f"https://api.smsbao.com/sms?u={username}&p={password}&m={phone}&c={encoded_content}"

                    # 创建SSL上下文
                    import ssl
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE

                    # 创建HTTPS handler
                    https_handler = urllib.request.HTTPSHandler(context=ssl_context)
                    opener.add_handler(https_handler)

                    request = urllib.request.Request(url)
                    response = opener.open(request, timeout=15)
                    result = response.read().decode('utf-8').strip()

                finally:
                    # 恢复代理设置
                    for key, value in old_proxies.items():
                        os.environ[key] = value

            # 处理结果
            if result == '0':
                print(f"📱 短信发送成功")
                return True
            else:
                error_codes = {
                    '30': '错误密码', '40': '账号不存在', '41': '余额不足',
                    '43': 'IP地址限制', '50': '内容含有敏感词', '51': '手机号码不正确'
                }
                error_msg = error_codes.get(result, f"未知错误: {result}")
                print(f"📱 短信发送失败: {error_msg}")
                return False

        except Exception as e:
            print(f"📱 短信发送异常: {e}")
            return False

    def _send_real_email(self, email, code):
        """发送真实邮件"""
        try:
            # 动态导入邮件服务
            try:
                from .email_service import EmailService
            except ImportError:
                # 如果相对导入失败，尝试绝对导入
                import sys
                import os
                sys.path.append(os.path.dirname(__file__))
                from email_service import EmailService

            email_service = EmailService()
            success, message = email_service.send_verification_code(email, code)

            return success, message

        except ImportError as e:
            print(f"📧 邮件服务模块导入失败: {e}")
            return False, "邮件服务不可用"
        except Exception as e:
            print(f"📧 邮件发送异常: {e}")
            return False, "邮件发送失败"



    def verify_code(self, contact, code):
        """验证验证码"""
        codes = self.load_codes()
        current_time = datetime.now()

        for code_data in codes:
            if (code_data['contact'] == contact and
                code_data['code'] == code and
                not code_data['used'] and
                datetime.fromisoformat(code_data['expires_at']) > current_time):

                # 标记为已使用
                code_data['used'] = True
                self.save_codes(codes)
                return True, "验证码验证成功"

        return False, "验证码无效或已过期"

class UserManager:
    def __init__(self, data_file='data/users.json', history_file='data/login_history.json', qr_file='data/qr_requests.json', oauth_clients_file='data/oauth_clients.json'):
        self.data_file = data_file
        self.history_file = history_file
        self.qr_file = qr_file
        self.oauth_clients_file = oauth_clients_file
        self.ensure_data_file()
        self.ensure_history_file()
        self.ensure_qr_file()
        self.ensure_oauth_clients_file()

    def ensure_data_file(self):
        """确保数据文件存在"""
        if not os.path.exists(self.data_file):
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump({"users": []}, f, ensure_ascii=False, indent=2)

    def ensure_history_file(self):
        """确保登录历史文件存在"""
        if not os.path.exists(self.history_file):
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump({"history": []}, f, ensure_ascii=False, indent=2)

    def ensure_qr_file(self):
        """确保二维码请求文件存在"""
        if not os.path.exists(self.qr_file):
            os.makedirs(os.path.dirname(self.qr_file), exist_ok=True)
            with open(self.qr_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)

    def ensure_oauth_clients_file(self):
        """确保OAuth客户端文件存在"""
        if not os.path.exists(self.oauth_clients_file):
            os.makedirs(os.path.dirname(self.oauth_clients_file), exist_ok=True)
            with open(self.oauth_clients_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
    
    def load_users(self):
        """加载用户数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('users', [])
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def save_users(self, users):
        """保存用户数据"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump({"users": users}, f, ensure_ascii=False, indent=2)
    
    def hash_password(self, password):
        """密码存储（明文）"""
        return password

    def verify_password(self, password, stored_password):
        """验证密码（明文比较）"""
        return password == stored_password
    
    def user_exists(self, username, email=None, phone=None):
        """检查用户是否存在"""
        users = self.load_users()
        for user in users:
            if user['username'] == username:
                return True
            if email and user.get('email') == email:
                return True
            if phone and user.get('phone') == phone:
                return True
        return False
    
    def create_user(self, username, password, email=None, phone=None):
        """创建新用户"""
        if self.user_exists(username, email, phone):
            return False, "用户名、邮箱或手机号已存在"

        users = self.load_users()
        new_user = {
            "id": str(uuid.uuid4()),
            "username": username,
            "email": email,
            "phone": phone,
            "password": self.hash_password(password),
            "created_at": datetime.now().isoformat(),
            "last_login": None,
            "register_type": "email" if email else "phone"
        }

        users.append(new_user)
        self.save_users(users)
        return True, "注册成功"
    
    def authenticate_user(self, username, password, ip_address=None, user_agent=None):
        """用户认证"""
        users = self.load_users()
        for user in users:
            if user['username'] == username:
                if self.verify_password(password, user['password']):
                    # 更新最后登录时间
                    user['last_login'] = datetime.now().isoformat()
                    self.save_users(users)

                    # 记录登录历史
                    self.record_login_history(user['id'], 'success', ip_address, user_agent)

                    return True, {
                        "id": user['id'],
                        "username": user['username'],
                        "email": user.get('email'),
                        "phone": user.get('phone'),
                        "register_type": user.get('register_type', 'email'),
                        "last_login": user['last_login']
                    }
                else:
                    # 记录失败的登录尝试
                    self.record_login_history(user['id'], 'failed', ip_address, user_agent)
                    return False, "密码错误"
        return False, "用户不存在"

    def load_login_history(self):
        """加载登录历史数据"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('history', [])
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def save_login_history(self, history):
        """保存登录历史数据"""
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump({"history": history}, f, ensure_ascii=False, indent=2)

    def record_login_history(self, user_id, status, ip_address=None, user_agent=None):
        """记录登录历史"""
        history = self.load_login_history()

        # 清理30天前的历史记录
        cutoff_date = datetime.now() - timedelta(days=30)
        history = [h for h in history if datetime.fromisoformat(h['login_time']) > cutoff_date]

        # 添加新记录
        new_record = {
            "id": str(uuid.uuid4()),
            "user_id": user_id,
            "login_time": datetime.now().isoformat(),
            "status": status,
            "ip_address": ip_address or "未知",
            "user_agent": user_agent or "未知"
        }

        history.append(new_record)
        self.save_login_history(history)

    def get_login_history(self, user_id, limit=10):
        """获取用户登录历史"""
        history = self.load_login_history()
        user_history = [h for h in history if h['user_id'] == user_id]

        # 按时间倒序排列
        user_history.sort(key=lambda x: x['login_time'], reverse=True)

        return user_history[:limit]

    def load_qr_requests(self):
        """加载二维码请求数据"""
        try:
            with open(self.qr_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def save_qr_requests(self, requests):
        """保存二维码请求数据"""
        with open(self.qr_file, 'w', encoding='utf-8') as f:
            json.dump(requests, f, ensure_ascii=False, indent=2)

    def load_oauth_clients(self):
        """加载OAuth客户端数据"""
        try:
            with open(self.oauth_clients_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def save_oauth_clients(self, clients):
        """保存OAuth客户端数据"""
        with open(self.oauth_clients_file, 'w', encoding='utf-8') as f:
            json.dump(clients, f, ensure_ascii=False, indent=2)

def validate_input(data, required_fields):
    """验证输入数据"""
    errors = []
    
    for field in required_fields:
        if field not in data or not data[field].strip():
            errors.append(f"{field}不能为空")
    
    if 'email' in data and data['email']:
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, data['email']):
            errors.append("邮箱格式不正确")

    if 'phone' in data and data['phone']:
        phone_pattern = r'^1[3-9]\d{9}$'
        if not re.match(phone_pattern, data['phone']):
            errors.append("手机号格式不正确")

    if 'password' in data and data['password']:
        if len(data['password']) < 6:
            errors.append("密码长度至少6位")

    if 'verification_code' in data and data['verification_code']:
        if len(data['verification_code']) != 6 or not data['verification_code'].isdigit():
            errors.append("验证码格式不正确")

    return errors
