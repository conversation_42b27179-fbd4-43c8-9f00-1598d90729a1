# 邮件服务配置说明

## 📧 阿里云邮件推送服务配置

### 1. 获取配置信息

#### 1.1 AccessKey信息
1. 登录 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 点击右上角头像 → AccessKey管理
3. 创建AccessKey，获取：
   - `AccessKeyId`：访问密钥ID
   - `AccessKeySecret`：访问密钥Secret（请妥善保管）

#### 1.2 邮件推送服务配置
1. 开通 [阿里云邮件推送服务](https://dm.console.aliyun.com/)
2. 配置发信域名：
   - 添加并验证您的域名
   - 配置DNS解析记录
3. 创建发信地址：
   - 格式：`<EMAIL>`
   - 记录此地址作为 `account_name`

### 2. 配置文件填写

编辑 `data/email_config.json` 文件：

```json
{
  "aliyun_dm": {
    "enabled": true,
    "region_id": "cn-hangzhou",
    "endpoint": "dm.aliyuncs.com",
    "access_key_id": "您的AccessKeyId",
    "access_key_secret": "您的AccessKeySecret", 
    "account_name": "<EMAIL>",
    "from_alias": "柠汐认证平台",
    "reply_to_address": false,
    "click_trace": "0"
  }
}
```

### 3. 配置参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `enabled` | 是否启用邮件服务 | `true` |
| `region_id` | 地域ID | `cn-hangzhou` |
| `endpoint` | API端点 | `dm.aliyuncs.com` |
| `access_key_id` | 访问密钥ID | `LTAI4G...` |
| `access_key_secret` | 访问密钥Secret | `abc123...` |
| `account_name` | 发信地址 | `<EMAIL>` |
| `from_alias` | 发件人别名 | `柠汐认证平台` |
| `reply_to_address` | 是否设置回复地址 | `false` |
| `click_trace` | 是否开启数据跟踪 | `"0"` |

### 4. 地域选择

根据您的需求选择合适的地域：

| 地域 | RegionId | Endpoint |
|------|----------|----------|
| 华东1（杭州） | `cn-hangzhou` | `dm.aliyuncs.com` |
| 新加坡 | `ap-southeast-1` | `dm.ap-southeast-1.aliyuncs.com` |
| 美国（悉尼） | `ap-southeast-2` | `dm.ap-southeast-2.aliyuncs.com` |
| 美国（新） | `us-east-1` | `dm.us-east-1.aliyuncs.com` |
| 德国（法兰克福） | `eu-central-1` | `dm.eu-central-1.aliyuncs.com` |

### 5. 安全建议

1. **AccessKey安全**：
   - 定期轮换AccessKey
   - 不要在代码中硬编码
   - 使用RAM子账号，最小权限原则

2. **发信限制**：
   - 设置合理的发信频率
   - 监控发信额度使用情况
   - 配置异常告警

3. **内容安全**：
   - 避免敏感词汇
   - 使用合规的邮件模板
   - 遵守反垃圾邮件规范

### 6. 测试验证

配置完成后，可以通过注册流程测试邮件发送功能：
1. 启动应用：`python app/app.py`
2. 访问注册页面
3. 选择邮箱注册
4. 输入邮箱地址并发送验证码
5. 检查邮箱是否收到验证码

### 7. 故障排除

#### 7.1 常见错误
- `InvalidAccessKeyId.NotFound`：AccessKeyId不正确
- `SignatureDoesNotMatch`：AccessKeySecret不正确或签名计算错误
- `InvalidAccountName.Malformed`：发信地址格式错误或未验证
- `DailyEmailNumberQuotaExceeded`：超出当日发送额度

#### 7.2 调试建议
1. 检查配置文件格式是否正确
2. 验证AccessKey是否有效
3. 确认发信地址已通过验证
4. 查看阿里云控制台的发信统计和错误日志

### 8. 费用说明

阿里云邮件推送服务按发送量计费：
- 免费额度：每月200封
- 超出部分：按量付费
- 详细价格请参考 [官方定价](https://www.aliyun.com/price/product#/dm/detail)

---

**注意**：请确保您的域名已完成ICP备案（如果选择国内地域），并遵守相关法律法规。
