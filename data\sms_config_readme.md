# 短信宝配置说明

## 配置文件位置
`data/sms_config.json`

## 需要填写的配置项

### 1. 短信宝账户信息
请在 `smsbao` 节点下填写以下信息：

- **username**: 短信宝注册的用户名
- **password**: 短信宝登录密码的MD5值（32位，不区分大小写）
- **api_key**: 短信宝的ApiKey（推荐使用，更安全）
- **goods_id**: 专用通道产品ID（可选，不填则使用通用短信产品）

### 2. 获取配置信息的步骤

1. **注册账号**
   - 访问 https://www.smsbao.com/reg 注册账号
   - 登录后台充值

2. **获取密码MD5值**
   - 方法1：使用在线MD5工具将您的密码转换为MD5
   - 方法2：在Python中使用 `import hashlib; hashlib.md5('您的密码'.encode()).hexdigest()`

3. **获取ApiKey（推荐）**
   - 登录短信宝后台
   - 联系客服获取ApiKey
   - 使用ApiKey比密码MD5更安全

4. **获取产品ID（可选）**
   - 如果使用专用通道产品，需要联系客服获取产品ID
   - 普通用户可以不填写此项

### 3. 启用短信功能
将 `enabled` 设置为 `true` 来启用短信发送功能

### 4. 配置示例
```json
{
  "smsbao": {
    "username": "your_username",
    "password": "9b11127a9701975c734b8aee81ee3526",
    "api_key": "your_api_key_here",
    "goods_id": "123456",
    "signature": "【重庆永柠科技】",
    "api_url": "https://api.smsbao.com/sms",
    "query_url": "https://api.smsbao.com/query",
    "enabled": true
  }
}
```

### 5. 注意事项

1. **密码安全**: 推荐使用ApiKey而不是密码MD5
2. **签名格式**: 签名必须是【】格式，3-10个字符
3. **内容规范**: 短信内容必须正规，不能发送测试性质的内容
4. **发送限制**: 默认相同号码一天只能接收4条短信
5. **模板报备**: 建议在短信宝后台报备短信模板以提高发送速度

### 6. 测试建议

1. 先使用单个号码测试
2. 发送正规的验证码内容
3. 避免给同一手机号连续发送相同内容
4. 如需大量测试，可在后台白名单设置中添加测试号码

### 7. 错误排查

如果发送失败，请检查：
- 用户名和密码/ApiKey是否正确
- 账户余额是否充足
- 手机号格式是否正确
- 短信内容是否包含敏感词
- IP是否被限制

详细错误代码请参考配置文件中的 `error_codes` 节点。
