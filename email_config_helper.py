#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件服务配置助手
帮助用户配置阿里云邮件推送服务
"""

import json
import os
import sys


def load_config():
    """加载邮件配置"""
    config_file = 'data/email_config.json'
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_file}")
        return None
    except json.JSONDecodeError:
        print(f"❌ 配置文件格式错误: {config_file}")
        return None


def save_config(config):
    """保存邮件配置"""
    config_file = 'data/email_config.json'
    try:
        os.makedirs(os.path.dirname(config_file), exist_ok=True)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False


def configure_aliyun_dm():
    """配置阿里云邮件推送服务"""
    print("\n📧 配置阿里云邮件推送服务")
    print("=" * 50)
    
    config = load_config()
    if not config:
        print("❌ 无法加载配置文件")
        return
    
    aliyun_config = config.get('aliyun_dm', {})
    
    print("\n请按照提示输入配置信息（直接回车保持当前值）:")
    
    # 地域选择
    print("\n可选地域:")
    regions = {
        '1': ('cn-hangzhou', 'dm.aliyuncs.com', '华东1（杭州）'),
        '2': ('ap-southeast-1', 'dm.ap-southeast-1.aliyuncs.com', '新加坡'),
        '3': ('ap-southeast-2', 'dm.ap-southeast-2.aliyuncs.com', '美国（悉尼）'),
        '4': ('us-east-1', 'dm.us-east-1.aliyuncs.com', '美国（新）'),
        '5': ('eu-central-1', 'dm.eu-central-1.aliyuncs.com', '德国（法兰克福）')
    }
    
    for key, (region_id, endpoint, name) in regions.items():
        current = "（当前）" if aliyun_config.get('region_id') == region_id else ""
        print(f"{key}. {name} {current}")
    
    region_choice = input(f"\n选择地域 (1-5) [当前: {aliyun_config.get('region_id', 'cn-hangzhou')}]: ").strip()
    if region_choice in regions:
        region_id, endpoint, _ = regions[region_choice]
        aliyun_config['region_id'] = region_id
        aliyun_config['endpoint'] = endpoint
        print(f"✅ 地域设置为: {regions[region_choice][2]}")
    
    # AccessKey配置
    print(f"\n当前AccessKeyId: {aliyun_config.get('access_key_id', '未设置')}")
    access_key_id = input("请输入AccessKeyId: ").strip()
    if access_key_id:
        aliyun_config['access_key_id'] = access_key_id
        print("✅ AccessKeyId已更新")
    
    print(f"\n当前AccessKeySecret: {'已设置' if aliyun_config.get('access_key_secret') else '未设置'}")
    access_key_secret = input("请输入AccessKeySecret: ").strip()
    if access_key_secret:
        aliyun_config['access_key_secret'] = access_key_secret
        print("✅ AccessKeySecret已更新")
    
    # 发信地址配置
    print(f"\n当前发信地址: {aliyun_config.get('account_name', '未设置')}")
    account_name = input("请输入发信地址 (如: <EMAIL>): ").strip()
    if account_name:
        aliyun_config['account_name'] = account_name
        print("✅ 发信地址已更新")
    
    # 发件人别名
    print(f"\n当前发件人别名: {aliyun_config.get('from_alias', '柠汐认证平台')}")
    from_alias = input("请输入发件人别名: ").strip()
    if from_alias:
        aliyun_config['from_alias'] = from_alias
        print("✅ 发件人别名已更新")
    
    # 启用状态
    current_enabled = aliyun_config.get('enabled', True)
    print(f"\n当前启用状态: {'启用' if current_enabled else '禁用'}")
    enable_choice = input("是否启用邮件服务? (y/n): ").strip().lower()
    if enable_choice in ['y', 'yes', '是']:
        aliyun_config['enabled'] = True
        print("✅ 邮件服务已启用")
    elif enable_choice in ['n', 'no', '否']:
        aliyun_config['enabled'] = False
        print("✅ 邮件服务已禁用")
    
    # 保存配置
    config['aliyun_dm'] = aliyun_config
    if save_config(config):
        print("\n✅ 配置保存成功！")
    else:
        print("\n❌ 配置保存失败！")


def show_current_config():
    """显示当前配置"""
    print("\n📧 当前邮件服务配置")
    print("=" * 50)
    
    config = load_config()
    if not config:
        return
    
    aliyun_config = config.get('aliyun_dm', {})
    
    print(f"服务状态: {'✅ 启用' if aliyun_config.get('enabled', False) else '❌ 禁用'}")
    print(f"地域: {aliyun_config.get('region_id', '未设置')}")
    print(f"端点: {aliyun_config.get('endpoint', '未设置')}")
    print(f"AccessKeyId: {aliyun_config.get('access_key_id', '未设置')}")
    print(f"AccessKeySecret: {'已设置' if aliyun_config.get('access_key_secret') else '未设置'}")
    print(f"发信地址: {aliyun_config.get('account_name', '未设置')}")
    print(f"发件人别名: {aliyun_config.get('from_alias', '未设置')}")
    
    # 配置完整性检查
    required_fields = ['access_key_id', 'access_key_secret', 'account_name']
    missing_fields = [field for field in required_fields if not aliyun_config.get(field)]
    
    if missing_fields:
        print(f"\n⚠️  配置不完整，缺少: {', '.join(missing_fields)}")
    else:
        print(f"\n✅ 配置完整")


def test_email_service():
    """测试邮件服务"""
    print("\n📧 测试邮件服务")
    print("=" * 50)
    
    try:
        # 导入邮件服务
        sys.path.append('app')
        from email_service import EmailService
        
        service = EmailService()
        success, message = service.test_connection()
        
        print(f"连接测试: {'✅' if success else '❌'} {message}")
        
        if success:
            test_email = input("\n请输入测试邮箱地址（用于发送测试邮件）: ").strip()
            if test_email:
                print(f"\n正在发送测试邮件到: {test_email}")
                success, message = service.send_verification_code(test_email, "123456")
                print(f"发送结果: {'✅' if success else '❌'} {message}")
                
                if success:
                    print("\n📧 请检查您的邮箱是否收到验证码邮件")
        
    except ImportError as e:
        print(f"❌ 邮件服务模块导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    """主函数"""
    print("🍋 柠汐认证平台 - 邮件服务配置助手")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 配置阿里云邮件推送服务")
        print("2. 查看当前配置")
        print("3. 测试邮件服务")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            configure_aliyun_dm()
        elif choice == '2':
            show_current_config()
        elif choice == '3':
            test_email_service()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == '__main__':
    main()
