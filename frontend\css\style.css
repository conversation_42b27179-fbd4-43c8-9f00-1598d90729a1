/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

/* 容器样式 */
.container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px;
}

/* 背景装饰 */
.background-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 认证容器 */
.auth-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Logo区域 */
.logo-section {
    text-align: center;
    margin-bottom: 40px;
}

.logo {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.logo:hover {
    transform: scale(1.05) rotate(5deg);
}

.platform-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.platform-subtitle {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 400;
}

/* 表单标题 */
.form-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    text-align: center;
}

.form-subtitle {
    font-size: 14px;
    color: #7f8c8d;
    text-align: center;
    margin-bottom: 30px;
}

/* 注册方式切换 */
.register-type-toggle {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
}

.type-btn {
    flex: 1;
    padding: 16px 12px;
    border: 2px solid #e9ecef;
    background: #fff;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.type-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.type-btn.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.type-btn .icon {
    font-size: 24px;
}

.type-btn .text {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
}

/* 验证码组 */
.verification-group {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.verification-input {
    flex: 1;
}

.send-code-btn {
    padding: 12px 16px;
    border: 2px solid #667eea;
    background: transparent;
    color: #667eea;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 100px;
}

.send-code-btn:hover {
    background: #667eea;
    color: white;
}

.send-code-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.send-code-btn .countdown {
    display: none;
}

.send-code-btn.counting .btn-text {
    display: none;
}

.send-code-btn.counting .countdown {
    display: inline;
}

/* 认证链接 */
.auth-links {
    text-align: center;
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;
}

.auth-links p {
    color: #6c757d;
    font-size: 14px;
}

.link-btn {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.link-btn:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 表单样式 */
.auth-form {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

.auth-form.active {
    display: block;
}

.auth-form:not(.active) {
    display: none;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-group {
    margin-bottom: 25px;
}

.input-container {
    position: relative;
}

.input-container input {
    width: 100%;
    padding: 16px 0 8px 0;
    border: none;
    border-bottom: 2px solid #e9ecef;
    background: transparent;
    font-size: 16px;
    color: #2c3e50;
    outline: none;
    transition: all 0.3s ease;
}

.input-container input:focus {
    border-bottom-color: #667eea;
}

.input-container input:focus + .floating-label,
.input-container input:valid + .floating-label {
    transform: translateY(-20px) scale(0.85);
    color: #667eea;
}

.floating-label {
    position: absolute;
    left: 0;
    top: 16px;
    color: #adb5bd;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    transform-origin: left top;
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.input-container input:focus ~ .input-line {
    width: 100%;
}

/* 提交按钮 */
.submit-btn {
    width: 100%;
    padding: 16px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: 20px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn.loading {
    pointer-events: none;
}

.submit-btn.loading .btn-text {
    opacity: 0;
}

.submit-btn.loading .btn-loader {
    opacity: 1;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.message {
    padding: 16px 24px;
    border-radius: 12px;
    margin-bottom: 10px;
    font-weight: 500;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    animation: slideInRight 0.3s ease-out;
    max-width: 350px;
}

.message.success {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
}

.message.error {
    background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
    color: white;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .auth-container {
        padding: 30px 20px;
        margin: 10px;
    }

    .platform-title {
        font-size: 24px;
    }

    .logo {
        width: 60px;
        height: 60px;
    }
}
