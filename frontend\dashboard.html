<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户中心 - 柠汐认证平台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 50px;
        }

        .user-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .user-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1.5" fill="white" opacity="0.1"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            margin: 0 auto 20px;
            position: relative;
            z-index: 1;
        }

        .user-info {
            position: relative;
            z-index: 1;
        }

        .user-name {
            font-size: 24px;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .user-status {
            font-size: 14px;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn-scan {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            font-size: 16px;
            padding: 15px 30px;
            margin-top: 10px;
        }

        .btn-scan:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-verified {
            background: #d4edda;
            color: #155724;
        }

        .status-unverified {
            background: #f8d7da;
            color: #721c24;
        }

        .binding-notice {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .binding-notice .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .binding-notice h3 {
            color: #856404;
            margin-bottom: 10px;
        }

        .binding-notice p {
            color: #856404;
            margin-bottom: 20px;
        }

        .qr-scanner {
            text-align: center;
            padding: 20px;
        }

        .qr-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #28a745;
            border-radius: 15px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fff9;
            font-size: 48px;
            color: #28a745;
        }

        .mobile-only {
            display: none;
        }

        .desktop-only {
            display: block;
        }

        @media (max-width: 768px) {
            .mobile-only {
                display: block;
            }
            .desktop-only {
                display: none;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-container {
                margin-top: 20px;
                padding: 15px;
            }
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            padding: 25px 30px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .close {
            font-size: 28px;
            cursor: pointer;
            color: rgba(255,255,255,0.8);
            line-height: 1;
            transition: all 0.3s ease;
            background: none;
            border: none;
            padding: 5px;
            border-radius: 50%;
        }

        .close:hover {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: 30px;
        }

        /* 登录历史样式 */
        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-info {
            flex: 1;
        }

        .history-time {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .history-details {
            font-size: 12px;
            color: #666;
        }

        .history-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .history-status.success {
            background: #d4edda;
            color: #155724;
        }

        .history-status.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .no-history {
            text-align: center;
            color: #999;
            padding: 40px 20px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 用户信息头部 -->
        <div class="user-header">
            <button class="logout-btn" onclick="logout()">退出登录</button>
            <div class="user-avatar">👤</div>
            <div class="user-info">
                <div class="user-name" id="userName">加载中...</div>
                <div class="user-status" id="userStatus">正在获取用户信息...</div>
            </div>
        </div>

        <!-- 绑定提醒 -->
        <div id="bindingNotice" class="binding-notice" style="display: none;">
            <div class="icon">⚠️</div>
            <h3 id="bindingTitle">需要绑定联系方式</h3>
            <p id="bindingMessage">为了您的账户安全，请绑定您的联系方式</p>
            <button class="btn btn-primary" onclick="showBindingForm()">立即绑定</button>
        </div>

        <!-- 功能卡片网格 -->
        <div class="dashboard-grid">
            <!-- 账户信息卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: #e3f2fd; color: #1976d2;">👤</div>
                    <div class="card-title">账户信息</div>
                </div>
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" id="usernameInput" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <input type="email" class="form-input" id="emailInput" style="flex: 1;">
                        <span id="emailStatus" class="status-badge status-unverified">未绑定</span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">手机号码</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <input type="tel" class="form-input" id="phoneInput" style="flex: 1;">
                        <span id="phoneStatus" class="status-badge status-unverified">未绑定</span>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="updateProfile()">更新信息</button>
            </div>

            <!-- 密码修改卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: #fff3e0; color: #f57c00;">🔒</div>
                    <div class="card-title">修改密码</div>
                </div>
                <div class="form-group">
                    <label class="form-label">当前密码</label>
                    <input type="password" class="form-input" id="currentPassword" placeholder="请输入当前密码">
                </div>
                <div class="form-group">
                    <label class="form-label">新密码</label>
                    <input type="password" class="form-input" id="newPassword" placeholder="请输入新密码">
                </div>
                <div class="form-group">
                    <label class="form-label">确认新密码</label>
                    <input type="password" class="form-input" id="confirmPassword" placeholder="请再次输入新密码">
                </div>
                <button class="btn btn-primary" onclick="changePassword()">修改密码</button>
            </div>

            <!-- 扫码登录卡片 (仅手机端显示) -->
            <div class="dashboard-card mobile-only">
                <div class="card-header">
                    <div class="card-icon" style="background: #e8f5e8; color: #2e7d32;">📱</div>
                    <div class="card-title">扫码登录</div>
                </div>
                <div class="qr-scanner">
                    <div class="qr-placeholder" id="qrPlaceholder">📷</div>
                    <p style="color: #666; margin-bottom: 20px;">扫描其他网站的二维码进行快速登录</p>
                    <button class="btn btn-scan" onclick="startQRScan()">
                        📱 开始扫码
                    </button>
                </div>
            </div>

            <!-- 登录历史卡片 -->
            <div class="dashboard-card">
                <div class="card-header">
                    <div class="card-icon" style="background: #f3e5f5; color: #7b1fa2;">📊</div>
                    <div class="card-title">登录历史</div>
                </div>
                <div id="loginHistory">
                    <p style="color: #666; text-align: center;">加载中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 绑定表单模态框 -->
    <div id="bindingModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="bindingModalTitle">绑定联系方式</h3>
                <span class="close" onclick="closeBindingModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="bindingForm">
                    <!-- 动态生成绑定表单 -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/dashboard.js"></script>
</body>
</html>
