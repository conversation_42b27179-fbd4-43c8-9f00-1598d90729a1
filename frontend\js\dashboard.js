// 用户中心JavaScript逻辑

let currentUser = null;
let qrScanner = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkLoginStatus();
    loadUserInfo();
    detectDevice();
});

// 检查登录状态
function checkLoginStatus() {
    const token = localStorage.getItem('auth_token');
    if (!token) {
        window.location.href = 'index.html';
        return;
    }
}

// 设备检测
function detectDevice() {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
        document.body.classList.add('mobile-device');
        // 移动端显示扫码功能
        document.querySelectorAll('.mobile-only').forEach(el => {
            el.style.display = 'block';
        });
        document.querySelectorAll('.desktop-only').forEach(el => {
            el.style.display = 'none';
        });
    } else {
        document.body.classList.add('desktop-device');
        // 桌面端隐藏扫码功能
        document.querySelectorAll('.mobile-only').forEach(el => {
            el.style.display = 'none';
        });
        document.querySelectorAll('.desktop-only').forEach(el => {
            el.style.display = 'block';
        });
    }
}

// 加载用户信息
async function loadUserInfo() {
    try {
        const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
        const response = await fetch('/api/user/profile', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-User-Info': localStorage.getItem('user_info') || '{}',
                'Content-Type': 'application/json'
            },
            credentials: 'include'
        });

        if (response.ok) {
            currentUser = await response.json();
            displayUserInfo(currentUser);
            checkBindingRequirement(currentUser);
        } else {
            throw new Error('获取用户信息失败');
        }
    } catch (error) {
        console.error('加载用户信息失败:', error);
        showMessage('获取用户信息失败，请重新登录', 'error');
        setTimeout(() => {
            logout();
        }, 2000);
    }
}

// 显示用户信息
function displayUserInfo(user) {
    document.getElementById('userName').textContent = user.username;
    document.getElementById('userStatus').textContent = `注册时间: ${formatDate(user.created_at)}`;
    
    // 填充表单
    document.getElementById('usernameInput').value = user.username;
    document.getElementById('emailInput').value = user.email || '';
    document.getElementById('phoneInput').value = user.phone || '';
    
    // 更新状态标签
    const emailStatus = document.getElementById('emailStatus');
    const phoneStatus = document.getElementById('phoneStatus');
    
    if (user.email) {
        emailStatus.textContent = '已绑定';
        emailStatus.className = 'status-badge status-verified';
    } else {
        emailStatus.textContent = '未绑定';
        emailStatus.className = 'status-badge status-unverified';
    }
    
    if (user.phone) {
        phoneStatus.textContent = '已绑定';
        phoneStatus.className = 'status-badge status-verified';
    } else {
        phoneStatus.textContent = '未绑定';
        phoneStatus.className = 'status-badge status-unverified';
    }
}

// 检查绑定要求
function checkBindingRequirement(user) {
    const bindingNotice = document.getElementById('bindingNotice');
    const bindingTitle = document.getElementById('bindingTitle');
    const bindingMessage = document.getElementById('bindingMessage');
    
    let needBinding = false;
    let bindingType = '';
    
    if (user.register_type === 'email' && !user.phone) {
        needBinding = true;
        bindingType = 'phone';
        bindingTitle.textContent = '需要绑定手机号';
        bindingMessage.textContent = '为了您的账户安全，请绑定您的手机号码以便接收重要通知';
    } else if (user.register_type === 'phone' && !user.email) {
        needBinding = true;
        bindingType = 'email';
        bindingTitle.textContent = '需要绑定邮箱';
        bindingMessage.textContent = '为了您的账户安全，请绑定您的邮箱地址以便接收重要通知';
    }
    
    if (needBinding) {
        bindingNotice.style.display = 'block';
        bindingNotice.setAttribute('data-binding-type', bindingType);
    } else {
        bindingNotice.style.display = 'none';
    }
}

// 显示绑定表单
function showBindingForm() {
    const bindingType = document.getElementById('bindingNotice').getAttribute('data-binding-type');
    const modal = document.getElementById('bindingModal');
    const modalTitle = document.getElementById('bindingModalTitle');
    const bindingForm = document.getElementById('bindingForm');
    
    if (bindingType === 'phone') {
        modalTitle.textContent = '绑定手机号';
        bindingForm.innerHTML = `
            <div class="form-group">
                <label class="form-label">手机号码</label>
                <input type="tel" class="form-input" id="bindingPhone" placeholder="请输入手机号码">
            </div>
            <div class="form-group">
                <label class="form-label">验证码</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" class="form-input" id="bindingPhoneCode" placeholder="请输入验证码" style="flex: 1;">
                    <button class="btn btn-secondary" onclick="sendBindingCode('phone')" id="sendPhoneCodeBtn">发送验证码</button>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="submitBinding('phone')">确认绑定</button>
                <button class="btn btn-secondary" onclick="closeBindingModal()" style="margin-left: 10px;">取消</button>
            </div>
        `;
    } else if (bindingType === 'email') {
        modalTitle.textContent = '绑定邮箱';
        bindingForm.innerHTML = `
            <div class="form-group">
                <label class="form-label">邮箱地址</label>
                <input type="email" class="form-input" id="bindingEmail" placeholder="请输入邮箱地址">
            </div>
            <div class="form-group">
                <label class="form-label">验证码</label>
                <div style="display: flex; gap: 10px;">
                    <input type="text" class="form-input" id="bindingEmailCode" placeholder="请输入验证码" style="flex: 1;">
                    <button class="btn btn-secondary" onclick="sendBindingCode('email')" id="sendEmailCodeBtn">发送验证码</button>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="submitBinding('email')">确认绑定</button>
                <button class="btn btn-secondary" onclick="closeBindingModal()" style="margin-left: 10px;">取消</button>
            </div>
        `;
    }
    
    modal.style.display = 'block';
}

// 关闭绑定模态框
function closeBindingModal() {
    document.getElementById('bindingModal').style.display = 'none';
}

// 发送绑定验证码
async function sendBindingCode(type) {
    const contact = type === 'phone' ? 
        document.getElementById('bindingPhone').value : 
        document.getElementById('bindingEmail').value;
    
    if (!contact) {
        showMessage(`请输入${type === 'phone' ? '手机号' : '邮箱地址'}`, 'error');
        return;
    }
    
    const button = document.getElementById(type === 'phone' ? 'sendPhoneCodeBtn' : 'sendEmailCodeBtn');
    const originalText = button.textContent;
    
    try {
        button.disabled = true;
        button.textContent = '发送中...';
        
        const response = await fetch('/send_verification_code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                contact: contact,
                contact_type: type
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('验证码已发送', 'success');
            startCountdown(button, 60);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('发送验证码失败:', error);
        showMessage('发送验证码失败: ' + error.message, 'error');
        button.disabled = false;
        button.textContent = originalText;
    }
}

// 提交绑定
async function submitBinding(type) {
    const contact = type === 'phone' ? 
        document.getElementById('bindingPhone').value : 
        document.getElementById('bindingEmail').value;
    const code = type === 'phone' ? 
        document.getElementById('bindingPhoneCode').value : 
        document.getElementById('bindingEmailCode').value;
    
    if (!contact || !code) {
        showMessage('请填写完整信息', 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/user/bind', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-User-Info': localStorage.getItem('user_info') || '{}',
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                type: type,
                contact: contact,
                code: code
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('绑定成功', 'success');
            closeBindingModal();
            loadUserInfo(); // 重新加载用户信息
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('绑定失败:', error);
        showMessage('绑定失败: ' + error.message, 'error');
    }
}

// 更新用户资料
async function updateProfile() {
    const email = document.getElementById('emailInput').value;
    const phone = document.getElementById('phoneInput').value;
    
    try {
        const response = await fetch('/api/user/profile', {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-User-Info': localStorage.getItem('user_info') || '{}',
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                email: email,
                phone: phone
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('资料更新成功', 'success');
            loadUserInfo(); // 重新加载用户信息
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('更新资料失败:', error);
        showMessage('更新资料失败: ' + error.message, 'error');
    }
}

// 修改密码
async function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        showMessage('请填写完整信息', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showMessage('两次输入的新密码不一致', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showMessage('新密码长度至少6位', 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/user/change_password', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                'X-User-Info': localStorage.getItem('user_info') || '{}',
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                current_password: currentPassword,
                new_password: newPassword
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('密码修改成功', 'success');
            // 清空密码输入框
            document.getElementById('currentPassword').value = '';
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('修改密码失败:', error);
        showMessage('修改密码失败: ' + error.message, 'error');
    }
}

// 开始二维码扫描
function startQRScan() {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        showMessage('您的设备不支持摄像头访问', 'error');
        return;
    }
    
    showMessage('二维码扫描功能开发中，敬请期待！', 'info');
    
    // TODO: 实现二维码扫描功能
    // 这里可以集成第三方二维码扫描库，如 qr-scanner
}

// 退出登录
function logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    window.location.href = '/';
}

// 工具函数
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function startCountdown(button, seconds) {
    let count = seconds;
    const timer = setInterval(() => {
        button.textContent = `${count}秒后重发`;
        count--;
        
        if (count < 0) {
            clearInterval(timer);
            button.disabled = false;
            button.textContent = '发送验证码';
        }
    }, 1000);
}

function showMessage(message, type = 'info') {
    // 创建消息提示
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    
    // 添加样式
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            messageDiv.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
            break;
        case 'error':
            messageDiv.style.background = 'linear-gradient(135deg, #dc3545, #e74c3c)';
            break;
        case 'warning':
            messageDiv.style.background = 'linear-gradient(135deg, #ffc107, #f39c12)';
            break;
        default:
            messageDiv.style.background = 'linear-gradient(135deg, #17a2b8, #3498db)';
    }
    
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .modal-content {
        background: white;
        border-radius: 15px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .modal-header {
        padding: 20px 25px;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h3 {
        margin: 0;
        color: #333;
    }
    
    .close {
        font-size: 24px;
        cursor: pointer;
        color: #999;
        line-height: 1;
    }
    
    .close:hover {
        color: #333;
    }
    
    .modal-body {
        padding: 25px;
    }
`;
document.head.appendChild(style);
