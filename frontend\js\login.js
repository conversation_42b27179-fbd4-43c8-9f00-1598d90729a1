// 柠汐认证平台 - 登录页面

class LoginApp {
    constructor() {
        this.apiBase = window.location.origin;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initForm();
    }

    initForm() {
        // 确保登录表单显示
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.classList.add('active');
        }
    }

    bindEvents() {
        // 表单提交事件
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 输入框焦点事件
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('blur', () => {
                this.validateInput(input);
            });
        });
    }

    validateInput(input) {
        const value = input.value.trim();
        const name = input.name;

        let isValid = true;
        let message = '';

        if (!value) {
            isValid = false;
            message = '此字段不能为空';
        } else {
            switch (name) {
                case 'username':
                    if (value.length < 3) {
                        isValid = false;
                        message = '用户名长度至少3位';
                    }
                    break;
                case 'password':
                    if (value.length < 6) {
                        isValid = false;
                        message = '密码长度至少6位';
                    }
                    break;
            }
        }

        this.updateInputState(input, isValid, message);
        return isValid;
    }

    updateInputState(input, isValid, message) {
        const container = input.closest('.input-container');
        
        // 移除之前的状态
        container.classList.remove('error', 'success');
        
        // 移除之前的错误消息
        const existingError = container.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        if (!isValid && message) {
            container.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            errorDiv.style.cssText = `
                color: #e74c3c;
                font-size: 12px;
                margin-top: 5px;
                animation: fadeIn 0.3s ease;
            `;
            container.appendChild(errorDiv);
        } else if (isValid && input.value.trim()) {
            container.classList.add('success');
        }
    }

    async handleLogin() {
        const form = document.getElementById('loginForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // 验证输入
        const inputs = form.querySelectorAll('input');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateInput(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            this.showMessage('请检查输入信息', 'error');
            return;
        }

        const submitBtn = form.querySelector('.submit-btn');
        this.setButtonLoading(submitBtn, true);

        try {
            const response = await fetch(`${this.apiBase}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('登录成功！', 'success');

                // 保存认证令牌和用户信息
                const authToken = 'auth_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('auth_token', authToken);
                localStorage.setItem('user_info', JSON.stringify(result.user));

                // 登录成功后跳转到用户中心
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            } else {
                this.showMessage(result.message || '登录失败', 'error');
            }
        } catch (error) {
            console.error('登录错误:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }

    showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;

        container.appendChild(messageDiv);

        // 自动移除消息
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.3s ease-in forwards';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }

    showUserInfo(user) {
        // 创建用户信息显示区域
        const authContainer = document.querySelector('.auth-container');
        authContainer.innerHTML = `
            <div class="user-info" style="text-align: center; animation: fadeIn 0.5s ease;">
                <div class="success-icon" style="font-size: 48px; color: #00b894; margin-bottom: 20px;">✓</div>
                <h2 style="color: #2c3e50; margin-bottom: 10px;">欢迎，${user.username}！</h2>
                <p style="color: #7f8c8d; margin-bottom: 20px;">登录成功</p>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; text-align: left;">
                    <p><strong>用户ID:</strong> ${user.id}</p>
                    <p><strong>用户名:</strong> ${user.username}</p>
                    ${user.email ? `<p><strong>邮箱:</strong> ${user.email}</p>` : ''}
                    ${user.phone ? `<p><strong>手机:</strong> ${user.phone}</p>` : ''}
                    <p><strong>注册方式:</strong> ${user.register_type === 'email' ? '邮箱' : '手机'}</p>
                    <p><strong>最后登录:</strong> ${new Date(user.last_login).toLocaleString()}</p>
                </div>
                <button onclick="location.reload()" style="
                    margin-top: 20px;
                    padding: 12px 24px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-weight: 500;
                ">重新登录</button>
            </div>
        `;
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new LoginApp();
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOutRight {
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
    
    .input-container.error input {
        border-bottom-color: #e74c3c !important;
    }
    
    .input-container.success input {
        border-bottom-color: #00b894 !important;
    }
`;
document.head.appendChild(style);
