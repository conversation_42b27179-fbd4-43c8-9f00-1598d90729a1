// 柠汐认证平台 - 注册页面

class RegisterApp {
    constructor() {
        this.apiBase = window.location.origin;
        this.currentType = 'email';
        this.countdownTimers = {};
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 注册方式切换事件
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchRegisterType(e.target.closest('.type-btn').dataset.type);
            });
        });

        // 表单提交事件
        document.getElementById('emailRegisterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister('email');
        });

        document.getElementById('phoneRegisterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister('phone');
        });

        // 发送验证码事件
        document.querySelectorAll('.send-code-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.sendVerificationCode(e.target.closest('.send-code-btn'));
            });
        });

        // 输入框焦点事件
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('blur', () => {
                this.validateInput(input);
            });
        });
    }

    switchRegisterType(type) {
        const typeBtns = document.querySelectorAll('.type-btn');
        const forms = document.querySelectorAll('.auth-form');

        // 更新按钮状态
        typeBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === type);
        });

        // 切换表单
        forms.forEach(form => {
            form.classList.remove('active');
        });

        setTimeout(() => {
            document.getElementById(type + 'RegisterForm').classList.add('active');
        }, 150);

        this.currentType = type;
        this.clearMessages();
    }

    validateInput(input) {
        const value = input.value.trim();
        const type = input.type;
        const name = input.name;

        let isValid = true;
        let message = '';

        if (!value) {
            isValid = false;
            message = '此字段不能为空';
        } else {
            switch (type) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        message = '请输入有效的邮箱地址';
                    }
                    break;
                case 'tel':
                    const phoneRegex = /^1[3-9]\d{9}$/;
                    if (!phoneRegex.test(value)) {
                        isValid = false;
                        message = '请输入有效的手机号码';
                    }
                    break;
                case 'password':
                    if (value.length < 6) {
                        isValid = false;
                        message = '密码长度至少6位';
                    }
                    break;
                case 'text':
                    if (name === 'username' && value.length < 3) {
                        isValid = false;
                        message = '用户名长度至少3位';
                    } else if (name === 'verification_code') {
                        if (value.length !== 6 || !/^\d{6}$/.test(value)) {
                            isValid = false;
                            message = '请输入6位数字验证码';
                        }
                    }
                    break;
            }
        }

        this.updateInputState(input, isValid, message);
        return isValid;
    }

    updateInputState(input, isValid, message) {
        const container = input.closest('.input-container');
        
        // 移除之前的状态
        container.classList.remove('error', 'success');
        
        // 移除之前的错误消息
        const existingError = container.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        if (!isValid && message) {
            container.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            errorDiv.style.cssText = `
                color: #e74c3c;
                font-size: 12px;
                margin-top: 5px;
                animation: fadeIn 0.3s ease;
            `;
            container.appendChild(errorDiv);
        } else if (isValid && input.value.trim()) {
            container.classList.add('success');
        }
    }

    async sendVerificationCode(button) {
        const contactType = button.dataset.contactType;
        const form = button.closest('.auth-form');
        const contactInput = form.querySelector(contactType === 'email' ? 'input[type="email"]' : 'input[type="tel"]');
        
        if (!contactInput || !this.validateInput(contactInput)) {
            this.showMessage(`请先输入有效的${contactType === 'email' ? '邮箱地址' : '手机号码'}`, 'error');
            return;
        }

        const contact = contactInput.value.trim();

        try {
            const response = await fetch(`${this.apiBase}/api/send-code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contact: contact,
                    contact_type: contactType
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage(result.message, 'success');
                this.startCountdown(button, 60);
            } else {
                this.showMessage(result.message || '发送失败', 'error');
            }
        } catch (error) {
            console.error('发送验证码错误:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        }
    }

    startCountdown(button, seconds) {
        button.disabled = true;
        button.classList.add('counting');
        
        const countdown = button.querySelector('.countdown');
        let remaining = seconds;

        const timer = setInterval(() => {
            countdown.textContent = `${remaining}s`;
            remaining--;

            if (remaining < 0) {
                clearInterval(timer);
                button.disabled = false;
                button.classList.remove('counting');
                countdown.textContent = '';
            }
        }, 1000);

        countdown.textContent = `${remaining}s`;
    }

    async handleRegister(registerType) {
        const form = document.getElementById(registerType + 'RegisterForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        data.register_type = registerType;

        // 验证输入
        const inputs = form.querySelectorAll('input');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateInput(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            this.showMessage('请检查输入信息', 'error');
            return;
        }

        const submitBtn = form.querySelector('.submit-btn');
        this.setButtonLoading(submitBtn, true);

        try {
            const response = await fetch(`${this.apiBase}/api/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('注册成功！正在跳转到登录页面...', 'success');
                // 清空表单
                form.reset();
                // 跳转到登录页面
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                this.showMessage(result.message || '注册失败', 'error');
                if (result.errors && result.errors.length > 0) {
                    result.errors.forEach(error => {
                        this.showMessage(error, 'error');
                    });
                }
            }
        } catch (error) {
            console.error('注册错误:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }

    showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;

        container.appendChild(messageDiv);

        // 自动移除消息
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.3s ease-in forwards';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }

    clearMessages() {
        const container = document.getElementById('messageContainer');
        container.innerHTML = '';
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new RegisterApp();
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOutRight {
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
    
    .input-container.error input {
        border-bottom-color: #e74c3c !important;
    }
    
    .input-container.success input {
        border-bottom-color: #00b894 !important;
    }
`;
document.head.appendChild(style);
