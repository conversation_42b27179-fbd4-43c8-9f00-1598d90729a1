<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫码授权 - 柠汐认证平台</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .auth-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 400px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .app-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
            color: white;
        }
        
        .client-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .user-info {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .auth-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn-authorize {
            flex: 1;
            background: #28a745;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-authorize:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .btn-deny {
            flex: 1;
            background: #dc3545;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-deny:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .loading {
            display: none;
            margin: 20px 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="auth-page">
        <div class="auth-card">
            <div class="app-icon">🔐</div>
            <h2 style="color: #333; margin-bottom: 10px;">扫码授权登录</h2>
            <p style="color: #666; margin-bottom: 30px;">第三方应用请求访问您的账户信息</p>
            
            <!-- 加载状态 -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p style="color: #666; margin-top: 15px;">正在验证请求...</p>
            </div>
            
            <!-- 错误信息 -->
            <div id="errorMessage" class="error-message" style="display: none;">
                <h4>授权失败</h4>
                <p id="errorText"></p>
            </div>
            
            <!-- 成功信息 -->
            <div id="successMessage" class="success-message" style="display: none;">
                <h4>授权成功！</h4>
                <p>您可以关闭此页面，返回第三方应用继续使用。</p>
            </div>
            
            <!-- 授权信息 -->
            <div id="authContent" style="display: none;">
                <!-- 第三方应用信息 -->
                <div class="client-info">
                    <h4 style="color: #333; margin-bottom: 10px;">请求应用</h4>
                    <p><strong>应用ID:</strong> <span id="clientId"></span></p>
                    <p style="margin-top: 10px; color: #666; font-size: 14px;">
                        该应用请求获取您的基本信息用于登录
                    </p>
                </div>
                
                <!-- 用户信息 -->
                <div class="user-info">
                    <h4 style="color: #333; margin-bottom: 10px;">当前用户</h4>
                    <div id="userDetails">
                        <!-- 用户信息将在这里显示 -->
                    </div>
                </div>
                
                <!-- 权限说明 -->
                <div style="background: #fff3cd; border-radius: 10px; padding: 15px; margin: 20px 0; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">授权后将共享以下信息：</h4>
                    <ul style="text-align: left; color: #856404; font-size: 14px; margin: 0; padding-left: 20px;">
                        <li>用户ID</li>
                        <li>用户名</li>
                        <li>邮箱地址（如已绑定）</li>
                        <li>手机号码（如已绑定）</li>
                    </ul>
                </div>
                
                <!-- 授权按钮 -->
                <div class="auth-buttons">
                    <button class="btn-deny" onclick="denyAuth()">拒绝</button>
                    <button class="btn-authorize" onclick="authorizeAuth()">授权登录</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let requestId = null;
        let clientId = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数获取请求信息
            const urlParams = new URLSearchParams(window.location.search);
            requestId = urlParams.get('request_id');
            clientId = urlParams.get('client_id');
            
            if (!requestId || !clientId) {
                showError('无效的授权请求参数');
                return;
            }
            
            // 检查用户登录状态
            checkUserLogin();
        });

        // 检查用户登录状态
        function checkUserLogin() {
            const authToken = localStorage.getItem('auth_token');
            const userInfo = localStorage.getItem('user_info');
            
            if (!authToken || !userInfo) {
                // 用户未登录，跳转到登录页面
                const loginUrl = `/index.html?redirect=${encodeURIComponent(window.location.href)}`;
                window.location.href = loginUrl;
                return;
            }
            
            try {
                const user = JSON.parse(userInfo);
                showAuthContent(user);
            } catch (error) {
                showError('用户信息解析失败');
            }
        }

        // 显示授权内容
        function showAuthContent(user) {
            // 检查用户是否已绑定手机号和邮箱
            if (!user.email || !user.phone) {
                showError('请先在用户中心绑定手机号和邮箱后再进行授权登录');
                return;
            }

            document.getElementById('loading').style.display = 'none';
            document.getElementById('authContent').style.display = 'block';

            // 显示应用信息
            document.getElementById('clientId').textContent = clientId;

            // 显示用户信息
            const userDetailsDiv = document.getElementById('userDetails');
            userDetailsDiv.innerHTML = `
                <p><strong>用户名:</strong> ${user.username}</p>
                <p><strong>邮箱:</strong> ${user.email}</p>
                <p><strong>手机:</strong> ${user.phone}</p>
            `;
        }

        // 授权登录
        async function authorizeAuth() {
            try {
                const authToken = localStorage.getItem('auth_token');
                const userInfo = localStorage.getItem('user_info');
                
                const response = await fetch('/api/qr/authorize', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'X-User-Info': userInfo,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        request_id: requestId,
                        client_id: clientId,
                        authorize: true
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess();
                } else {
                    showError(result.message || '授权失败');
                }
            } catch (error) {
                console.error('授权失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 拒绝授权
        async function denyAuth() {
            try {
                const authToken = localStorage.getItem('auth_token');
                const userInfo = localStorage.getItem('user_info');
                
                const response = await fetch('/api/qr/authorize', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'X-User-Info': userInfo,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        request_id: requestId,
                        client_id: clientId,
                        authorize: false
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showError('您已拒绝授权，可以关闭此页面');
                } else {
                    showError(result.message || '操作失败');
                }
            } catch (error) {
                console.error('拒绝授权失败:', error);
                showError('网络错误，请重试');
            }
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('authContent').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
            
            document.getElementById('errorText').textContent = message;
            document.getElementById('errorMessage').style.display = 'block';
        }

        // 显示成功信息
        function showSuccess() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('authContent').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
            
            document.getElementById('successMessage').style.display = 'block';
            
            // 3秒后自动关闭页面
            setTimeout(() => {
                window.close();
            }, 3000);
        }
    </script>
</body>
</html>
