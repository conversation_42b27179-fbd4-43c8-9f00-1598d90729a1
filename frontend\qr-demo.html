<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码登录演示 - 柠汐认证平台</title>
    <link rel="stylesheet" href="css/style.css">
    <!-- 引入二维码生成库 -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
    <style>
        .demo-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }
        
        .demo-card {
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            text-align: center;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            border: 2px solid #28a745;
            border-radius: 15px;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fff9;
            font-size: 48px;
            color: #28a745;
            overflow: hidden;
        }

        .qr-code canvas {
            max-width: 100%;
            max-height: 100%;
            border-radius: 10px;
        }
        
        .status-indicator {
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 500;
            margin: 15px 0;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-authorized {
            background: #d4edda;
            color: #155724;
        }
        
        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 10px;">二维码登录演示</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            模拟第三方网站请求二维码登录的完整流程
        </p>
        
        <div class="demo-grid">
            <!-- 第三方网站端 -->
            <div class="demo-card">
                <h3 style="color: #333; margin-bottom: 20px;">第三方网站</h3>
                <div id="qrCode" class="qr-code">
                    点击生成二维码
                </div>
                <div id="loginStatus" class="status-indicator status-pending">
                    等待扫码
                </div>
                <button class="btn btn-primary" onclick="generateQRCode()" style="margin: 10px;">
                    生成登录二维码
                </button>
                <div id="userInfo" style="margin-top: 20px; display: none;">
                    <h4>登录成功！</h4>
                    <div id="userDetails" style="text-align: left; background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 10px;">
                        <!-- 用户信息将在这里显示 -->
                    </div>
                </div>
            </div>
            
            <!-- 用户手机端 -->
            <div class="demo-card">
                <h3 style="color: #333; margin-bottom: 20px;">用户手机端</h3>
                <p style="color: #666; margin-bottom: 20px;">
                    在实际使用中，用户会在柠汐认证平台的用户中心点击右上角的扫码按钮
                </p>
                <button class="btn btn-scan" onclick="openDashboard()" style="margin: 10px;">
                    📱 打开用户中心
                </button>
                <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 10px;">
                    <h4 style="color: #1976d2; margin-bottom: 10px;">使用说明：</h4>
                    <ol style="text-align: left; color: #666; font-size: 14px;">
                        <li>点击"生成登录二维码"</li>
                        <li>点击"打开用户中心"</li>
                        <li>在用户中心点击右上角扫码按钮</li>
                        <li>扫描左侧的二维码</li>
                        <li>确认授权登录</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentRequestId = null;
        let statusCheckInterval = null;

        // 生成二维码
        async function generateQRCode() {
            try {
                const response = await fetch('/api/qr/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        client_id: 'demo_client',
                        callback_url: window.location.origin + '/qr-demo.html'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    currentRequestId = result.request_id;

                    // 生成真正的二维码
                    const qrCodeDiv = document.getElementById('qrCode');
                    qrCodeDiv.innerHTML = ''; // 清空内容

                    // 使用qrcode.js生成二维码
                    QRCode.toCanvas(result.qr_url, {
                        width: 180,
                        height: 180,
                        margin: 2,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        }
                    }, function (error, canvas) {
                        if (error) {
                            console.error('生成二维码失败:', error);
                            qrCodeDiv.innerHTML = `
                                <div style="font-size: 12px; line-height: 1.2; color: #333;">
                                    <div style="margin-bottom: 10px;">❌</div>
                                    <div>二维码生成失败</div>
                                </div>
                            `;
                        } else {
                            qrCodeDiv.appendChild(canvas);
                        }
                    });

                    document.getElementById('loginStatus').textContent = '等待扫码授权';
                    document.getElementById('loginStatus').className = 'status-indicator status-pending';

                    // 开始检查状态
                    startStatusCheck();
                } else {
                    alert('生成二维码失败: ' + result.message);
                }
            } catch (error) {
                console.error('生成二维码失败:', error);
                alert('生成二维码失败');
            }
        }

        // 开始状态检查
        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                if (!currentRequestId) return;

                try {
                    const response = await fetch(`/api/qr/status/${currentRequestId}`);
                    const result = await response.json();

                    if (result.success) {
                        const status = result.status;
                        const statusElement = document.getElementById('loginStatus');
                        
                        switch (status) {
                            case 'pending':
                                statusElement.textContent = '等待扫码授权';
                                statusElement.className = 'status-indicator status-pending';
                                break;
                            case 'authorized':
                                statusElement.textContent = '授权成功！';
                                statusElement.className = 'status-indicator status-authorized';
                                showUserInfo(result.user_data);
                                clearInterval(statusCheckInterval);
                                break;
                            case 'denied':
                                statusElement.textContent = '用户拒绝授权';
                                statusElement.className = 'status-indicator status-expired';
                                clearInterval(statusCheckInterval);
                                break;
                            case 'expired':
                                statusElement.textContent = '二维码已过期';
                                statusElement.className = 'status-indicator status-expired';
                                clearInterval(statusCheckInterval);
                                break;
                        }
                    }
                } catch (error) {
                    console.error('检查状态失败:', error);
                }
            }, 2000); // 每2秒检查一次
        }

        // 显示用户信息
        function showUserInfo(userData) {
            const userInfoDiv = document.getElementById('userInfo');
            const userDetailsDiv = document.getElementById('userDetails');
            
            userDetailsDiv.innerHTML = `
                <p><strong>用户ID:</strong> ${userData.user_id}</p>
                <p><strong>用户名:</strong> ${userData.username}</p>
                <p><strong>邮箱:</strong> ${userData.email || '未绑定'}</p>
                <p><strong>手机:</strong> ${userData.phone || '未绑定'}</p>
            `;
            
            userInfoDiv.style.display = 'block';
        }

        // 打开用户中心
        function openDashboard() {
            window.open('/dashboard.html', '_blank');
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
        });
    </script>
</body>
</html>
