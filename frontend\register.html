<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柠汐认证平台 - 注册</title>
    <link rel="stylesheet" href="/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="auth-container">
            <!-- Logo区域 -->
            <div class="logo-section">
                <img src="https://d.ly-y.cn/up/diugai.com175413109649212.png" alt="柠汐认证平台" class="logo">
                <h1 class="platform-title">柠汐认证平台</h1>
                <p class="platform-subtitle">安全 · 便捷 · 可靠</p>
            </div>

            <!-- 注册表单区域 -->
            <div class="form-container">
                <h2 class="form-title">创建账户</h2>
                <p class="form-subtitle">选择您的注册方式</p>

                <!-- 注册方式切换 -->
                <div class="register-type-toggle">
                    <button class="type-btn active" data-type="email">
                        <span class="icon">📧</span>
                        <span class="text">邮箱注册</span>
                    </button>
                    <button class="type-btn" data-type="phone">
                        <span class="icon">📱</span>
                        <span class="text">手机注册</span>
                    </button>
                </div>

                <!-- 邮箱注册表单 -->
                <form id="emailRegisterForm" class="auth-form active">
                    <div class="form-group">
                        <div class="input-container">
                            <input type="text" id="emailUsername" name="username" required>
                            <label for="emailUsername" class="floating-label">用户名</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <input type="email" id="emailAddress" name="email" required>
                            <label for="emailAddress" class="floating-label">邮箱地址</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="verification-group">
                            <div class="input-container verification-input">
                                <input type="text" id="emailVerificationCode" name="verification_code" required maxlength="6">
                                <label for="emailVerificationCode" class="floating-label">验证码</label>
                                <div class="input-line"></div>
                            </div>
                            <button type="button" class="send-code-btn" data-contact-type="email">
                                <span class="btn-text">发送验证码</span>
                                <span class="countdown"></span>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <input type="password" id="emailPassword" name="password" required>
                            <label for="emailPassword" class="floating-label">密码</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">
                        <span class="btn-text">注册账户</span>
                        <div class="btn-loader"></div>
                    </button>
                </form>

                <!-- 手机注册表单 -->
                <form id="phoneRegisterForm" class="auth-form">
                    <div class="form-group">
                        <div class="input-container">
                            <input type="text" id="phoneUsername" name="username" required>
                            <label for="phoneUsername" class="floating-label">用户名</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <input type="tel" id="phoneNumber" name="phone" required maxlength="11">
                            <label for="phoneNumber" class="floating-label">手机号码</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="verification-group">
                            <div class="input-container verification-input">
                                <input type="text" id="phoneVerificationCode" name="verification_code" required maxlength="6">
                                <label for="phoneVerificationCode" class="floating-label">验证码</label>
                                <div class="input-line"></div>
                            </div>
                            <button type="button" class="send-code-btn" data-contact-type="phone">
                                <span class="btn-text">发送验证码</span>
                                <span class="countdown"></span>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <input type="password" id="phonePassword" name="password" required>
                            <label for="phonePassword" class="floating-label">密码</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">
                        <span class="btn-text">注册账户</span>
                        <div class="btn-loader"></div>
                    </button>
                </form>

                <!-- 登录链接 -->
                <div class="auth-links">
                    <p>已有账户？ <a href="/" class="link-btn">立即登录</a></p>
                </div>
            </div>
        </div>

        <!-- 消息提示 -->
        <div id="messageContainer" class="message-container"></div>
    </div>

    <script src="/js/register.js"></script>
</body>
</html>
