#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信配置助手 - 帮助用户配置短信宝服务
"""

import json
import os
import hashlib
import getpass

def load_config():
    """加载配置文件"""
    config_file = 'data/sms_config.json'
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_file}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return None

def save_config(config):
    """保存配置文件"""
    config_file = 'data/sms_config.json'
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def md5_hash(text):
    """计算MD5哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def configure_smsbao():
    """配置短信宝服务"""
    print("🍋 柠汐认证平台 - 短信宝配置助手")
    print("=" * 50)
    
    # 加载现有配置
    config = load_config()
    if not config:
        return False
    
    smsbao_config = config.get('smsbao', {})
    
    print("请按照提示输入短信宝配置信息:")
    print("(直接回车保持当前值不变)")
    print()
    
    # 配置用户名
    current_username = smsbao_config.get('username', '')
    if current_username:
        print(f"当前用户名: {current_username}")
    username = input("短信宝用户名: ").strip()
    if username:
        smsbao_config['username'] = username
    elif not current_username:
        print("❌ 用户名不能为空")
        return False
    
    # 配置密码或ApiKey
    print("\n选择认证方式:")
    print("1. 使用密码 (将自动转换为MD5)")
    print("2. 使用ApiKey (推荐，更安全)")
    
    auth_choice = input("请选择 (1/2): ").strip()
    
    if auth_choice == '1':
        # 使用密码
        password = getpass.getpass("短信宝密码: ")
        if password:
            password_md5 = md5_hash(password)
            smsbao_config['password'] = password_md5
            smsbao_config['api_key'] = ""  # 清空ApiKey
            print(f"✅ 密码MD5: {password_md5}")
        elif not smsbao_config.get('password'):
            print("❌ 密码不能为空")
            return False
    
    elif auth_choice == '2':
        # 使用ApiKey
        current_api_key = smsbao_config.get('api_key', '')
        if current_api_key:
            print(f"当前ApiKey: {current_api_key[:10]}...")
        api_key = input("短信宝ApiKey: ").strip()
        if api_key:
            smsbao_config['api_key'] = api_key
            smsbao_config['password'] = ""  # 清空密码
        elif not current_api_key:
            print("❌ ApiKey不能为空")
            return False
    
    else:
        print("❌ 无效选择")
        return False
    
    # 配置产品ID（可选）
    current_goods_id = smsbao_config.get('goods_id', '')
    if current_goods_id:
        print(f"\n当前产品ID: {current_goods_id}")
    goods_id = input("产品ID (可选，专用通道使用): ").strip()
    if goods_id:
        smsbao_config['goods_id'] = goods_id
    
    # 配置签名
    current_signature = smsbao_config.get('signature', '【重庆永柠科技】')
    print(f"\n当前签名: {current_signature}")
    signature = input("短信签名 (格式: 【公司名称】): ").strip()
    if signature:
        if signature.startswith('【') and signature.endswith('】'):
            smsbao_config['signature'] = signature
        else:
            print("❌ 签名格式错误，应为【公司名称】格式")
            return False
    
    # 启用服务
    print(f"\n当前服务状态: {'启用' if smsbao_config.get('enabled') else '禁用'}")
    enable_choice = input("是否启用短信服务? (y/N): ").strip().lower()
    smsbao_config['enabled'] = enable_choice == 'y'
    
    # 保存配置
    config['smsbao'] = smsbao_config
    
    if save_config(config):
        print("\n✅ 配置保存成功!")
        print("\n当前配置:")
        print(f"  用户名: {smsbao_config.get('username')}")
        if smsbao_config.get('password'):
            print(f"  密码MD5: {smsbao_config.get('password')}")
        if smsbao_config.get('api_key'):
            print(f"  ApiKey: {smsbao_config.get('api_key')[:10]}...")
        if smsbao_config.get('goods_id'):
            print(f"  产品ID: {smsbao_config.get('goods_id')}")
        print(f"  签名: {smsbao_config.get('signature')}")
        print(f"  状态: {'启用' if smsbao_config.get('enabled') else '禁用'}")
        
        if smsbao_config.get('enabled'):
            print("\n🎉 短信服务已启用！")
            print("您现在可以使用 python test_sms.py 测试短信功能")
        else:
            print("\n⚠️  短信服务未启用，将使用模拟发送")
        
        return True
    else:
        return False

def show_current_config():
    """显示当前配置"""
    config = load_config()
    if not config:
        return
    
    smsbao_config = config.get('smsbao', {})
    
    print("🍋 当前短信宝配置:")
    print("=" * 30)
    print(f"用户名: {smsbao_config.get('username', '未配置')}")
    
    if smsbao_config.get('password'):
        print(f"密码MD5: {smsbao_config.get('password')}")
    
    if smsbao_config.get('api_key'):
        print(f"ApiKey: {smsbao_config.get('api_key')[:10]}...")
    
    if smsbao_config.get('goods_id'):
        print(f"产品ID: {smsbao_config.get('goods_id')}")
    
    print(f"签名: {smsbao_config.get('signature', '未配置')}")
    print(f"状态: {'启用' if smsbao_config.get('enabled') else '禁用'}")

def main():
    """主函数"""
    while True:
        print("\n🍋 柠汐认证平台 - 短信配置管理")
        print("=" * 40)
        print("1. 配置短信宝服务")
        print("2. 查看当前配置")
        print("3. 退出")

        choice = input("\n请选择操作 (1-3): ").strip()

        if choice == '1':
            configure_smsbao()
        elif choice == '2':
            show_current_config()
        elif choice == '3':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == '__main__':
    main()
