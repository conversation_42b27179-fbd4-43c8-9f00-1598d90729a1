注册及注意事项
1.用户可访问 https://www.smsbao.com/reg 自助注册账号、充值。

2.在短信宝上无须做任何配置即可直接发送短信，但通过在平台报备短信模板，审核通过后，发送速度将提高到5秒左右。故建议客户申请好模板后再进行测试。

3.群发短信将进入人工审核。故测试时尽量用单个号码，以保证接收速度。

4.为防止骚扰，默认相同号码一天只能接收4条短信，如果测试期间使用短信较多，可以在后台白名单设置中添加，即可取消限制。

短信格式要求
1.测试短信时，也请发送正规内容短信，如验证码、订单通知等，千万不要发送无意义的内容：例如 测一下、您好。

正确范例：【万千购】您的验证码是1234,30秒内有效.若非本人操作请忽略此消息

2.短信内容中必须含短信签名格式为：【公司签名】 ，【】中间的文字可以是公司简称、网站简称、注册商标名称，3～10个字符，如 好乐迪，志邦橱柜。不能是无意义的签名，例如【测一下】【试试看】等。

API接口列表
短信接口列表

国内短信API文档:查看

国内短信：

国内短信API
测试时，请使用正式内容进行测试，请勿给同一手机号连续发送相同内容，运营商已进行规则调整以应对短信炸弹问题。

所有短信均有监控，请勿发送以下内容（不合法，要挟、虚假、私人信息、滥发类）。

网页应用用于手机验证 ，请务必增加图形验证（以防止被人利用，被用于短信轰炸用途）。

接口列表
描述	请求地址
手机短信发送	http://api.smsbao.com/sms?u=USERNAME&p=PASSWORD&m=PHONE&c=CONTENT
短信接收API	http://您的域名/接收参数的文件?m=PHONE&c=CONTENT
查询余额API	http://api.smsbao.com/query?u=USERNAME&p=PASSWORD
编码
统一使用 UTF-8 编码,请确保您的文件编码及传入参数的格式

发送国内短信接口API
功能说明
通过此接口，短信宝接收客户提交的短信单发/群发请求。使用该接口时，必须提前在短信宝短信后台申请VIP通道模板并且联系客服报备

接口地址
普通接口:   http://api.smsbao.com/sms?u=USERNAME&p=PASSWORD&g=GOODSID&m=PHONE&c=CONTENT

安全接口:   https://api.smsbao.com/sms?u=USERNAME&p=PASSWORD&g=GOODSID&m=PHONE&c=CONTENT

HTTP请求方式
GET
短信宝客户->短信宝平台

请求参数
参数名	是否必须	示例值	描述
u	是	tom	在本短信平台注册的用户名
p	是	9b11127a9701975c734b8aee81ee3526	平台登录密码MD5后的值(32位，不区分大小写)
或者ApiKey(推荐使用更安全)可在短信宝后台或联系客服获得
g	否	123456	当客户使用专用通道产品时，需要指定产品ID
产品ID可在短信宝后台或联系客服获得,不填则默认使用通用短信产品
m	是	单发：15205201314
群发：15205201314,15205201315	接收的手机号;群发时多个手机号以逗号分隔，一次不要超过99个号码
c	是	%E3%80%90%E7%9F%AD%E4%BF%A1%E5%AE%9D%E3%80%91%E6%82%A8%E7%9A%84%E9%AA%8C%E8%AF%81%E7%A0%81%E6%98%AF4564	发送内容，采用UTF-8 URL ENCODE
代码示例：UrlEncode(“【短信宝】您的验证码是4564”, “UTF-8”)
返回结果
返回 ‘0’ 视为发送成功，其他内容为错误提示内容

错误代码列表
错误码	错误描述
30	错误密码
40	账号不存在
41	余额不足
43	IP地址限制
50	内容含有敏感词
51	手机号码不正确
国内短信接收推送API
功能说明
通过此接口，我们将为您实时推送最新的用户回复短信。您需要提供一个url地址，接受http get请求。 并在短信宝短信后台中设置好。该接口一次推送1个手机用户回复信息，为不影响推送速度，建议先接受数据后再做异步处理。

HTTP请求方式
GET
短信宝平台->短信宝客户

请求参数
参数名	是否必须	示例值	描述
m	是	15205201314	发送方的手机号
c	是	%E3%80%90%E7%9F%AD%E4%BF%A1%E5%AE%9D%E3%80%91%E6%82%A8%E7%9A%84%E9%AA%8C%E8%AF%81%E7%A0%81%E6%98%AF4564	用户回复的短信内容,采用UTF-8 URLENCODE
代码示例：UrlEncode(“【短信宝】您的验证码是4564”, “UTF-8”)
返回结果
接收到数据后，CP处理成功请返回字符串”0″，其他返回值将被认为是失败，若失败，我们将会于一分钟，三分钟，十分钟后重试推送三次。若接口1小时内累计调用10次都获取不到正确返回值，将暂停推送1小时

查询账户余额API
功能说明
获取当前账号余额

调用频率不要超过1次/分钟

接口地址
普通接口:   https://www.smsbao.com/query?u=USERNAME&p=PASSWORD

安全接口:   https://www.smsbao.com/query?u=USERNAME&p=PASSWORD

HTTP请求方式
GET
短信宝客户->短信宝平台

请求参数
参数名	是否必须	示例值	描述
u	是	tom	在本短信平台注册的用户名
p	是	9b11127a9701975c734b8aee81ee3526	平台登录密码MD5后的值(32位，不区分大小写)
或者ApiKey(推荐使用更安全) 可在短信宝后台或联系客服获得
返回结果
第一行返回 ‘0’ 视为发送成功，其他内容为错误提示内容 如果第一行返回成功，则第二行返回 ‘发送条数,剩余条数’


