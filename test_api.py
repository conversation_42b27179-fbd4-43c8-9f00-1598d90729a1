#!/usr/bin/env python3
"""
API测试脚本
用于测试柠汐认证平台的各个API接口
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_health():
    """测试健康检查接口"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康检查: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_register():
    """测试注册接口"""
    try:
        # 发送验证码
        code_response = requests.post(f"{BASE_URL}/api/send-code", json={
            "contact": "<EMAIL>",
            "contact_type": "email"
        })
        print(f"发送验证码: {code_response.status_code} - {code_response.json()}")
        
        # 注册用户
        register_response = requests.post(f"{BASE_URL}/api/register", json={
            "username": "testuser",
            "password": "123456",
            "email": "<EMAIL>",
            "verification_code": "123456"  # 使用固定验证码进行测试
        })
        print(f"用户注册: {register_response.status_code} - {register_response.json()}")
        return register_response.status_code == 200
    except Exception as e:
        print(f"注册测试失败: {e}")
        return False

def test_login():
    """测试登录接口"""
    try:
        response = requests.post(f"{BASE_URL}/api/login", json={
            "username": "testuser",
            "password": "123456"
        })
        print(f"用户登录: {response.status_code} - {response.json()}")
        
        if response.status_code == 200:
            user_data = response.json().get('user', {})
            return user_data.get('id'), user_data
        return None, None
    except Exception as e:
        print(f"登录测试失败: {e}")
        return None, None

def test_login_history(user_id, user_data):
    """测试登录历史接口"""
    try:
        headers = {
            'Authorization': 'Bearer fake_token',
            'X-User-Info': json.dumps(user_data),
            'Content-Type': 'application/json'
        }
        
        response = requests.get(f"{BASE_URL}/api/user/login_history", headers=headers)
        print(f"登录历史: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"登录历史测试失败: {e}")
        return False

def test_qr_flow():
    """测试二维码登录流程"""
    try:
        # 生成二维码
        qr_response = requests.post(f"{BASE_URL}/api/qr/generate", json={
            "client_id": "test_client",
            "callback_url": "http://localhost:5000/test"
        })
        print(f"生成二维码: {qr_response.status_code} - {qr_response.json()}")
        
        if qr_response.status_code == 200:
            request_id = qr_response.json().get('request_id')
            
            # 检查状态
            status_response = requests.get(f"{BASE_URL}/api/qr/status/{request_id}")
            print(f"检查状态: {status_response.status_code} - {status_response.json()}")
            
            return status_response.status_code == 200
        return False
    except Exception as e:
        print(f"二维码流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("柠汐认证平台 API 测试")
    print("=" * 50)
    
    # 测试健康检查
    print("\n1. 测试健康检查接口")
    if not test_health():
        print("❌ 服务器未启动或健康检查失败")
        return
    print("✅ 健康检查通过")
    
    # 测试注册
    print("\n2. 测试用户注册")
    if test_register():
        print("✅ 注册测试通过")
    else:
        print("⚠️ 注册测试失败（可能用户已存在）")
    
    # 测试登录
    print("\n3. 测试用户登录")
    user_id, user_data = test_login()
    if user_id:
        print("✅ 登录测试通过")
        
        # 测试登录历史
        print("\n4. 测试登录历史")
        if test_login_history(user_id, user_data):
            print("✅ 登录历史测试通过")
        else:
            print("❌ 登录历史测试失败")
    else:
        print("❌ 登录测试失败")
    
    # 测试二维码流程
    print("\n5. 测试二维码登录流程")
    if test_qr_flow():
        print("✅ 二维码流程测试通过")
    else:
        print("❌ 二维码流程测试失败")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
